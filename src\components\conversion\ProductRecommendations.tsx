import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchFeaturedProducts, fetchBestSellerProducts, fetchProducts } from '../../store/slices/productSlice';
import { ProductGridSkeleton, LoadingState } from '../common/ProductLoadingStates';
import { ProductsError, EmptyState } from '../common/ErrorStates';
import {
  StarIcon,
  ShoppingCartIcon,
  HeartIcon,
  ArrowRightIcon,
  FireIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon, HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { Product } from '../../types';

interface RecommendationProps {
  type: 'related' | 'frequently_bought' | 'recently_viewed' | 'trending' | 'you_might_like';
  currentProduct?: Product;
  category?: string;
  limit?: number;
  className?: string;
}

interface RecommendationSet {
  title: string;
  subtitle: string;
  icon: React.ComponentType<any>;
  products: Product[];
  badge?: string;
  badgeColor?: string;
}

const ProductRecommendations: React.FC<RecommendationProps> = ({
  type,
  currentProduct,
  category,
  limit = 4,
  className = ''
}) => {
  const dispatch = useAppDispatch();
  const { featuredProducts, bestSellerProducts, products, loading } = useAppSelector((state) => state.products);
  const [recommendations, setRecommendations] = useState<RecommendationSet | null>(null);
  const [wishlist, setWishlist] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);

  // Fetch data based on recommendation type
  const fetchRecommendationData = async () => {
    setIsLoading(true);
    try {
      switch (type) {
        case 'you_might_like':
          if (featuredProducts.length === 0) {
            await dispatch(fetchFeaturedProducts(limit));
          }
          break;
        case 'trending':
          if (bestSellerProducts.length === 0) {
            await dispatch(fetchBestSellerProducts(limit));
          }
          break;
        case 'related':
          if (currentProduct && products.length === 0) {
            await dispatch(fetchProducts({
              category: currentProduct.categoryId?.toString(),
              limit: limit + 1 // Get one extra to exclude current product
            }));
          }
          break;
        default:
          if (products.length === 0) {
            await dispatch(fetchProducts({ limit }));
          }
          break;
      }
    } catch (error) {
      console.error('Failed to fetch recommendation data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendationData();
  }, [type, currentProduct, limit]);

  useEffect(() => {
    // Build recommendations from fetched data
    const buildRecommendations = () => {
      let recommendationSet: RecommendationSet;
      let sourceProducts: Product[] = [];

      // Ensure we have safe arrays to work with
      const safeProducts = products || [];
      const safeBestSellerProducts = bestSellerProducts || [];
      const safeFeaturedProducts = featuredProducts || [];

      switch (type) {
        case 'related':
          sourceProducts = safeProducts.filter(p =>
            currentProduct ? p.categoryId === currentProduct.categoryId && p.id !== currentProduct.id : true
          ).slice(0, limit);
          recommendationSet = {
            title: 'Related Products',
            subtitle: 'Customers who viewed this item also viewed',
            icon: SparklesIcon,
            products: sourceProducts
          };
          break;

        case 'frequently_bought':
          sourceProducts = safeProducts.slice(0, limit);
          recommendationSet = {
            title: 'Frequently Bought Together',
            subtitle: 'Complete your order with these popular combinations',
            icon: ShoppingCartIcon,
            products: sourceProducts,
            badge: 'Bundle & Save',
            badgeColor: 'bg-green-100 text-green-800'
          };
          break;

        case 'recently_viewed':
          sourceProducts = safeProducts.slice(1, limit + 1);
          recommendationSet = {
            title: 'Recently Viewed',
            subtitle: 'Continue where you left off',
            icon: ArrowRightIcon,
            products: sourceProducts
          };
          break;

        case 'trending':
          sourceProducts = safeBestSellerProducts.slice(0, limit);
          recommendationSet = {
            title: 'Trending Now',
            subtitle: 'What\'s popular with other customers',
            icon: FireIcon,
            products: sourceProducts,
            badge: 'Hot',
            badgeColor: 'bg-red-100 text-red-800'
          };
          break;

        case 'you_might_like':
        default:
          sourceProducts = safeFeaturedProducts.slice(0, limit);
          recommendationSet = {
            title: 'You Might Also Like',
            subtitle: 'Handpicked recommendations just for you',
            icon: HeartIcon,
            products: sourceProducts
          };
          break;
      }

      setRecommendations(recommendationSet);
    };

    // Only build recommendations if we're not loading and have data
    if (!isLoading) {
      buildRecommendations();
    }
  }, [type, currentProduct, limit, featuredProducts, bestSellerProducts, products, isLoading]);

  const toggleWishlist = (productId: number) => {
    const productIdStr = productId.toString();
    setWishlist(prev => {
      const newWishlist = new Set(prev);
      if (newWishlist.has(productIdStr)) {
        newWishlist.delete(productIdStr);
      } else {
        newWishlist.add(productIdStr);
      }
      return newWishlist;
    });
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarSolidIcon
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  // Show loading state
  if (isLoading || loading) {
    return (
      <div className={`bg-white py-12 ${className}`}>
        <div className="container mx-auto px-4">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(limit)].map((_, i) => (
                <div key={i} className="bg-gray-200 rounded-lg h-64"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!recommendations || recommendations.products.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white py-12 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <recommendations.icon className="h-6 w-6 text-primary-600 mr-3" />
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{recommendations.title}</h2>
              <p className="text-gray-600">{recommendations.subtitle}</p>
            </div>
          </div>
          {recommendations.badge && (
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${recommendations.badgeColor}`}>
              {recommendations.badge}
            </span>
          )}
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {recommendations.products.map((product) => (
            <div key={product.id} className="group relative bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
              {/* Product Image */}
              <div className="relative aspect-square overflow-hidden">
                <Link to={`/product/${product.slug}`}>
                  <img
                    src={product.images[0]?.url}
                    alt={product.images[0]?.alt || product.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      e.currentTarget.src = '/images/placeholder-product.jpg';
                    }}
                  />
                </Link>

                {/* Badges */}
                <div className="absolute top-3 left-3 space-y-2">
                  {product.salePrice && (
                    <span className="bg-red-600 text-white text-xs font-bold px-2 py-1 rounded">
                      SALE
                    </span>
                  )}
                  {product.bestSeller && (
                    <span className="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded">
                      BEST SELLER
                    </span>
                  )}
                  {product.featured && (
                    <span className="bg-primary-600 text-white text-xs font-bold px-2 py-1 rounded">
                      FEATURED
                    </span>
                  )}
                </div>

                {/* Wishlist Button */}
                <button
                  onClick={() => toggleWishlist(product.id)}
                  className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow"
                >
                  {wishlist.has(product.id.toString()) ? (
                    <HeartSolidIcon className="h-5 w-5 text-red-500" />
                  ) : (
                    <HeartIcon className="h-5 w-5 text-gray-400 hover:text-red-500" />
                  )}
                </button>

                {/* Quick Add to Cart */}
                <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center">
                    <ShoppingCartIcon className="h-4 w-4 mr-2" />
                    Quick Add
                  </button>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4">
                <div className="mb-2">
                  <span className="text-xs font-medium text-primary-600 uppercase tracking-wide">
                    {product.cannabinoid}
                  </span>
                </div>

                <Link to={`/product/${product.slug}`}>
                  <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-primary-600 transition-colors">
                    {product.name}
                  </h3>
                </Link>

                {/* Rating */}
                <div className="flex items-center mb-2">
                  {renderStars(product.averageRating)}
                  <span className="text-xs text-gray-500 ml-2">
                    ({product.reviewCount})
                  </span>
                </div>

                {/* Price */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {product.salePrice ? (
                      <>
                        <span className="text-lg font-bold text-red-600">
                          ${product.salePrice.toFixed(2)}
                        </span>
                        <span className="text-sm text-gray-500 line-through">
                          ${product.price.toFixed(2)}
                        </span>
                      </>
                    ) : (
                      <span className="text-lg font-bold text-gray-900">
                        ${product.price.toFixed(2)}
                      </span>
                    )}
                  </div>

                  {/* Stock indicator */}
                  {product.stock <= 5 && (
                    <span className="text-xs text-red-600 font-medium">
                      Only {product.stock} left
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Link */}
        {type !== 'recently_viewed' && (
          <div className="text-center mt-8">
            <Link
              to={category ? `/shop/${category}` : '/shop'}
              className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium"
            >
              View All Products
              <ArrowRightIcon className="h-4 w-4 ml-2" />
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

// Bundle recommendation component
export const BundleRecommendation: React.FC<{
  mainProduct: Product;
  bundleProducts: Product[];
  discount?: number;
  className?: string;
}> = ({ mainProduct, bundleProducts, discount = 15, className = '' }) => {
  const totalPrice = [mainProduct, ...bundleProducts].reduce((sum, product) => sum + product.price, 0);
  const bundlePrice = totalPrice * (1 - discount / 100);
  const savings = totalPrice - bundlePrice;

  return (
    <div className={`bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 ${className}`}>
      <div className="flex items-center mb-4">
        <ShoppingCartIcon className="h-6 w-6 text-green-600 mr-3" />
        <div>
          <h3 className="text-lg font-bold text-gray-900">Complete Your Bundle</h3>
          <p className="text-sm text-gray-600">Save {discount}% when you buy together</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {[mainProduct, ...bundleProducts].map((product, index) => (
          <div key={product.id} className="flex items-center space-x-3">
            {index > 0 && <span className="text-gray-400">+</span>}
            <img
              src={product.images[0]?.url}
              alt={product.name}
              className="w-16 h-16 object-cover rounded-lg"
              onError={(e) => {
                e.currentTarget.src = '/images/placeholder-product.jpg';
              }}
            />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 line-clamp-2">{product.name}</p>
              <p className="text-sm text-gray-600">${product.price.toFixed(2)}</p>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-green-600">${bundlePrice.toFixed(2)}</span>
            <span className="text-sm text-gray-500 line-through">${totalPrice.toFixed(2)}</span>
          </div>
          <p className="text-sm text-green-600 font-medium">Save ${savings.toFixed(2)}</p>
        </div>
        <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
          Add Bundle to Cart
        </button>
      </div>
    </div>
  );
};

export default ProductRecommendations;
