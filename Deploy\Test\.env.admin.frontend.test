# Admin Frontend Test Environment Configuration
# All variables must be prefixed with VITE_ to be accessible in the frontend

# Environment
VITE_NODE_ENV=testing
VITE_ENVIRONMENT=test
VITE_APP_MODE=admin

# API Configuration
VITE_API_URL=https://test.shopnirvanaorganics.com/api/admin
VITE_API_BASE_URL=https://test.shopnirvanaorganics.com
VITE_BACKEND_URL=https://test.shopnirvanaorganics.com

# Domain Configuration
VITE_DOMAIN=test.shopnirvanaorganics.com
VITE_FRONTEND_URL=https://test.shopnirvanaorganics.com/admin
VITE_ADMIN_URL=https://test.shopnirvanaorganics.com/admin
VITE_MAIN_SITE_URL=https://test.shopnirvanaorganics.com

# Application Configuration
VITE_APP_NAME=Nirvana Organics Admin Test
VITE_APP_VERSION=1.0.0-test

# Square Payment Configuration (Public keys only)
VITE_SQUARE_APPLICATION_ID=your-square-sandbox-application-id
VITE_SQUARE_ENVIRONMENT=sandbox
VITE_SQUARE_LOCATION_ID=your-square-sandbox-location-id

# Google Services
VITE_GOOGLE_CLIENT_ID=your-test-google-client-id
VITE_GOOGLE_ANALYTICS_ID=your-test-google-analytics-id
VITE_GOOGLE_MAPS_API_KEY=your-test-google-maps-api-key

# Social Media
VITE_FACEBOOK_APP_ID=your-test-facebook-app-id
VITE_INSTAGRAM_HANDLE=nirvanaorganics_test
VITE_TWITTER_HANDLE=nirvanaorganics_test

# Features
VITE_ENABLE_PWA=false
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_CHAT_SUPPORT=false

# Security
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_REDIRECT=true
VITE_DISABLE_RIGHT_CLICK=true
VITE_DISABLE_DEV_TOOLS=false

# Performance
VITE_ENABLE_SERVICE_WORKER=false
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_IMAGE_OPTIMIZATION=true

# Contact Information
VITE_COMPANY_NAME=Nirvana Organics
VITE_COMPANY_EMAIL=<EMAIL>
VITE_COMPANY_PHONE=******-123-4567
VITE_COMPANY_ADDRESS=123 Wellness Way, Denver, CO 80202

# SEO Configuration (Test)
VITE_SITE_NAME=Nirvana Organics Admin Test
VITE_SITE_DESCRIPTION=Admin panel for Nirvana Organics test environment
VITE_SITE_KEYWORDS=admin, management, cannabis, hemp, CBD, THC-A

# Error Tracking (Test)
VITE_SENTRY_DSN=your-test-sentry-dsn-here
VITE_ENABLE_ERROR_TRACKING=true

# CDN Configuration (Test)
VITE_CDN_URL=https://test-cdn.shopnirvanaorganics.com
VITE_ASSETS_URL=https://test.shopnirvanaorganics.com/assets

# Debug Configuration
VITE_DEBUG_MODE=true
VITE_ENABLE_CONSOLE_LOGS=true
VITE_ENABLE_REDUX_DEVTOOLS=true

# Build Configuration
VITE_BUILD_VERSION=1.0.0-test
VITE_BUILD_TIMESTAMP=

# API Timeouts
VITE_API_TIMEOUT=60000

# Feature Flags
VITE_ENABLE_GOOGLE_OAUTH=true
VITE_ENABLE_SQUARE_PAYMENTS=true
VITE_ENABLE_STRIPE_PAYMENTS=false
VITE_ENABLE_PAYPAL_PAYMENTS=false

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_ANIMATIONS=true

# File Upload Configuration (Admin limits - higher)
VITE_MAX_FILE_SIZE=52428800
VITE_ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp,svg
VITE_ALLOWED_DOCUMENT_TYPES=pdf,doc,docx,xls,xlsx,csv

# Rate Limiting (Frontend display)
VITE_RATE_LIMIT_DISPLAY=true
VITE_RATE_LIMIT_WARNING_THRESHOLD=80

# Admin Configuration
VITE_ADMIN_SESSION_TIMEOUT=28800000
VITE_ENABLE_ADMIN_ANALYTICS=true
VITE_ENABLE_DATA_ENVIRONMENT=true

# Data Management
VITE_ENABLE_BULK_OPERATIONS=true
VITE_ENABLE_DATA_EXPORT=true
VITE_ENABLE_DATA_IMPORT=true
VITE_ENABLE_BACKUP_RESTORE=true

# Reporting
VITE_ENABLE_ADVANCED_REPORTING=true
VITE_ENABLE_REAL_TIME_ANALYTICS=true
VITE_ENABLE_CUSTOM_REPORTS=true

# User Management
VITE_ENABLE_USER_MANAGEMENT=true
VITE_ENABLE_ROLE_MANAGEMENT=true
VITE_ENABLE_PERMISSION_MANAGEMENT=true

# Product Management
VITE_ENABLE_PRODUCT_MANAGEMENT=true
VITE_ENABLE_CATEGORY_MANAGEMENT=true
VITE_ENABLE_INVENTORY_MANAGEMENT=true

# Order Management
VITE_ENABLE_ORDER_MANAGEMENT=true
VITE_ENABLE_ORDER_TRACKING=true
VITE_ENABLE_SHIPPING_MANAGEMENT=true

# Content Management
VITE_ENABLE_CMS=true
VITE_ENABLE_BLOG_MANAGEMENT=true
VITE_ENABLE_PAGE_MANAGEMENT=true

# Marketing
VITE_ENABLE_COUPON_MANAGEMENT=true
VITE_ENABLE_DISCOUNT_MANAGEMENT=true
VITE_ENABLE_EMAIL_CAMPAIGNS=true

# Social Media Management
VITE_ENABLE_SOCIAL_MEDIA_MANAGEMENT=true
VITE_ENABLE_SOCIAL_ANALYTICS=true
VITE_ENABLE_SOCIAL_SCHEDULING=true

# Customer Support
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_ADMIN_EMAIL=<EMAIL>
VITE_SUPPORT_PHONE=******-123-4567

# Cache Configuration
VITE_CACHE_STATIC_ASSETS=true
VITE_CACHE_API_RESPONSES=false
VITE_CACHE_DURATION=1800000

# Performance Configuration
VITE_ENABLE_COMPRESSION=true
VITE_ENABLE_MINIFICATION=true
VITE_ENABLE_TREE_SHAKING=true

# Testing Configuration
VITE_ENABLE_TEST_MODE=true
VITE_TEST_ADMIN_EMAIL=<EMAIL>
VITE_TEST_ADMIN_PASSWORD=Admin123!

# Maintenance Mode
VITE_MAINTENANCE_MODE=false
VITE_MAINTENANCE_MESSAGE=Admin panel is currently under maintenance. Please check back soon.

# Security Features
VITE_ENABLE_2FA=false
VITE_ENABLE_IP_WHITELIST=false
VITE_ENABLE_SESSION_MONITORING=true

# Audit Logging
VITE_ENABLE_AUDIT_LOGGING=true
VITE_ENABLE_ACTION_LOGGING=true
VITE_ENABLE_ACCESS_LOGGING=true

# Data Environment
VITE_DATA_ENVIRONMENT_MODE=test
VITE_ENABLE_MOCK_DATA=true
VITE_ENABLE_TEST_DATA=true

# Notifications
VITE_ENABLE_ADMIN_NOTIFICATIONS=true
VITE_ENABLE_SYSTEM_ALERTS=true
VITE_ENABLE_EMAIL_NOTIFICATIONS=true

# Dashboard
VITE_ENABLE_DASHBOARD_WIDGETS=true
VITE_ENABLE_CUSTOM_DASHBOARD=true
VITE_ENABLE_REAL_TIME_UPDATES=true

# API Configuration
VITE_ENABLE_API_DOCUMENTATION=true
VITE_ENABLE_API_TESTING=true
VITE_ENABLE_WEBHOOK_MANAGEMENT=true

# System Monitoring
VITE_ENABLE_SYSTEM_MONITORING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_MONITORING=true

# Backup & Recovery
VITE_ENABLE_AUTOMATED_BACKUPS=true
VITE_BACKUP_RETENTION_DAYS=90
VITE_ENABLE_POINT_IN_TIME_RECOVERY=true
