import React from 'react';
import SkeletonLoader from './SkeletonLoader';

interface ProductCardSkeletonProps {
  count?: number;
  className?: string;
}

export const ProductCardSkeleton: React.FC<ProductCardSkeletonProps> = ({ 
  count = 1, 
  className = '' 
}) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}>
          {/* Product Image Skeleton */}
          <div className="relative aspect-square bg-gray-200 animate-pulse">
            <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer"></div>
          </div>
          
          {/* Product Info Skeleton */}
          <div className="p-4 space-y-3">
            {/* Product Name */}
            <SkeletonLoader height="1.25rem" width="85%" />
            
            {/* Product Description */}
            <SkeletonLoader height="0.875rem" width="100%" lines={2} />
            
            {/* Price and Rating */}
            <div className="flex items-center justify-between">
              <SkeletonLoader height="1.5rem" width="4rem" />
              <SkeletonLoader height="1rem" width="5rem" />
            </div>
            
            {/* Add to Cart Button */}
            <SkeletonLoader height="2.5rem" width="100%" variant="rounded" />
          </div>
        </div>
      ))}
    </>
  );
};

interface ProductGridSkeletonProps {
  count?: number;
  columns?: 2 | 3 | 4 | 5;
  className?: string;
}

export const ProductGridSkeleton: React.FC<ProductGridSkeletonProps> = ({ 
  count = 8, 
  columns = 4,
  className = '' 
}) => {
  const gridClasses = {
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5'
  };

  return (
    <div className={`grid ${gridClasses[columns]} gap-6 ${className}`}>
      <ProductCardSkeleton count={count} />
    </div>
  );
};

export const ProductDetailSkeleton: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Images Skeleton */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="aspect-square bg-gray-200 rounded-lg animate-pulse">
            <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer rounded-lg"></div>
          </div>
          
          {/* Thumbnail Images */}
          <div className="flex space-x-2">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="w-20 h-20 bg-gray-200 rounded-lg animate-pulse">
                <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer rounded-lg"></div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Product Info Skeleton */}
        <div className="space-y-6">
          {/* Product Name */}
          <SkeletonLoader height="2rem" width="80%" />
          
          {/* Rating */}
          <div className="flex items-center space-x-2">
            <SkeletonLoader height="1.25rem" width="6rem" />
            <SkeletonLoader height="1rem" width="4rem" />
          </div>
          
          {/* Price */}
          <SkeletonLoader height="2.5rem" width="8rem" />
          
          {/* Description */}
          <div className="space-y-2">
            <SkeletonLoader height="1rem" width="100%" lines={4} />
          </div>
          
          {/* Variants */}
          <div className="space-y-3">
            <SkeletonLoader height="1.25rem" width="6rem" />
            <div className="flex space-x-2">
              {Array.from({ length: 3 }).map((_, index) => (
                <SkeletonLoader key={index} height="2.5rem" width="4rem" variant="rounded" />
              ))}
            </div>
          </div>
          
          {/* Quantity and Add to Cart */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <SkeletonLoader height="2.5rem" width="8rem" variant="rounded" />
              <SkeletonLoader height="2.5rem" width="12rem" variant="rounded" />
            </div>
          </div>
          
          {/* Product Features */}
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-3">
                <SkeletonLoader height="1.5rem" width="1.5rem" variant="circular" />
                <SkeletonLoader height="1rem" width="10rem" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export const BestSellersSkeleton: React.FC = () => {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header Skeleton */}
        <div className="text-center mb-12">
          <SkeletonLoader height="2.5rem" width="12rem" className="mx-auto mb-4" />
          <SkeletonLoader height="1.25rem" width="20rem" className="mx-auto" />
        </div>
        
        {/* Products Grid Skeleton */}
        <ProductGridSkeleton count={8} columns={4} />
        
        {/* View All Button Skeleton */}
        <div className="text-center mt-12">
          <SkeletonLoader height="3rem" width="8rem" variant="rounded" className="mx-auto" />
        </div>
      </div>
    </section>
  );
};

export const CategoryGridSkeleton: React.FC = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header Skeleton */}
        <div className="text-center mb-12">
          <SkeletonLoader height="2.5rem" width="16rem" className="mx-auto mb-4" />
          <SkeletonLoader height="1.25rem" width="24rem" className="mx-auto" />
        </div>
        
        {/* Categories Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Category Image */}
              <div className="h-48 bg-gray-200 animate-pulse">
                <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer"></div>
              </div>
              
              {/* Category Info */}
              <div className="p-6 space-y-3">
                <SkeletonLoader height="1.5rem" width="70%" />
                <SkeletonLoader height="1rem" width="100%" lines={2} />
                <SkeletonLoader height="2.5rem" width="6rem" variant="rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Loading state with animated spinner and message
interface LoadingStateProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({ 
  message = 'Loading amazing products...', 
  size = 'medium',
  className = '' 
}) => {
  const sizeClasses = {
    small: 'py-8',
    medium: 'py-16',
    large: 'py-24'
  };

  const spinnerSizes = {
    small: 'h-8 w-8',
    medium: 'h-12 w-12',
    large: 'h-16 w-16'
  };

  return (
    <div className={`flex flex-col items-center justify-center text-center ${sizeClasses[size]} ${className}`}>
      {/* Animated Spinner */}
      <div className="relative mb-6">
        <div className={`${spinnerSizes[size]} border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin`}></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-2xl animate-pulse">🌿</div>
        </div>
      </div>
      
      {/* Loading Message */}
      <p className="text-gray-600 text-lg font-medium animate-pulse">
        {message}
      </p>
      
      {/* Loading Dots */}
      <div className="flex space-x-1 mt-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className="w-2 h-2 bg-primary-400 rounded-full animate-bounce"
            style={{ animationDelay: `${index * 0.2}s` }}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default ProductCardSkeleton;
