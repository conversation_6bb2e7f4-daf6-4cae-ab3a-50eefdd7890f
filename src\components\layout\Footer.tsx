import React from 'react';
import { Link } from 'react-router-dom';
import SocialMediaIcons from '../common/SocialMediaIcons';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <img
                src="/Nirvana_logo.png"
                alt="Nirvana Organics Logo"
                className="h-14 sm:h-16 w-auto filter brightness-0 invert"
                onError={(e) => {
                  // Fallback to text if logo fails to load
                  e.currentTarget.style.display = 'none';
                }}
              />
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              Premium hemp-derived cannabis products designed to enhance your lifestyle. 
              Discover our flowers, chocolates, pre-rolls, diamond sauce, and vapes for 
              relaxation, focus, and well-being.
            </p>
            <SocialMediaIcons
              size="md"
              variant="default"
              className="text-gray-300 hover:text-white"
            />
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/shop" className="text-gray-300 hover:text-white transition-colors">
                  Shop All
                </Link>
              </li>
              <li>
                <Link to="/shop?bestSeller=true" className="text-gray-300 hover:text-white transition-colors">
                  Best Sellers
                </Link>
              </li>
              <li>
                <Link to="/shop?featured=true" className="text-gray-300 hover:text-white transition-colors">
                  Featured Products
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-gray-300 hover:text-white transition-colors">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Categories</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/shop?cannabinoid=CBD" className="text-gray-300 hover:text-white transition-colors">
                  CBD Products
                </Link>
              </li>
              <li>
                <Link to="/shop?cannabinoid=Delta-8" className="text-gray-300 hover:text-white transition-colors">
                  Delta-8 THC
                </Link>
              </li>
              <li>
                <Link to="/shop?cannabinoid=Delta-9" className="text-gray-300 hover:text-white transition-colors">
                  Delta-9 THC
                </Link>
              </li>
              <li>
                <Link to="/shop?cannabinoid=THC-A" className="text-gray-300 hover:text-white transition-colors">
                  THC-A Products
                </Link>
              </li>
              <li>
                <Link to="/track-order" className="text-gray-300 hover:text-white transition-colors">
                  Track Your Order
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-300 text-sm mb-4 md:mb-0">
              © {currentYear} Nirvana Organics. All rights reserved.
            </div>
            <div className="flex space-x-6 text-sm">
              <Link to="/privacy" className="text-gray-300 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link to="/terms" className="text-gray-300 hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link to="/shipping" className="text-gray-300 hover:text-white transition-colors">
                Shipping Info
              </Link>
              <Link to="/returns" className="text-gray-300 hover:text-white transition-colors">
                Returns
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
