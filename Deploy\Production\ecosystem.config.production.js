module.exports = {
  apps: [
    {
      name: 'nirvana-main-prod',
      script: 'server/index.js',
      cwd: '/var/www/nirvana-production',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 5000,
        PM2_SERVE_PATH: '/var/www/nirvana-production',
        PM2_SERVE_PORT: 5000,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/index.html'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      log_file: '/var/www/nirvana-production/logs/combined.log',
      out_file: '/var/www/nirvana-production/logs/out.log',
      error_file: '/var/www/nirvana-production/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '2G',
      node_args: '--max-old-space-size=2048',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      autorestart: true,
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        '.git'
      ],
      source_map_support: false,
      instance_var: 'INSTANCE_ID',
      combine_logs: true,
      log_type: 'json',
      time: true
    },
    {
      name: 'nirvana-admin-prod',
      script: 'server/admin-server.js',
      cwd: '/var/www/nirvana-production',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        PM2_SERVE_PATH: '/var/www/nirvana-production',
        PM2_SERVE_PORT: 3001,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/admin.html'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      log_file: '/var/www/nirvana-production/logs/admin-combined.log',
      out_file: '/var/www/nirvana-production/logs/admin-out.log',
      error_file: '/var/www/nirvana-production/logs/admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '2G',
      node_args: '--max-old-space-size=2048',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      autorestart: true,
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        '.git'
      ],
      source_map_support: false,
      instance_var: 'INSTANCE_ID',
      combine_logs: true,
      log_type: 'json',
      time: true
    }
  ],
  
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/nirvana-organics.git',
      path: '/var/www/nirvana-production',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --production && npm run build:prod && npm run build:admin:prod && pm2 reload ecosystem.config.production.js --env production',
      'pre-setup': ''
    }
  }
};
