import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchBestSellerProducts } from '../../store/slices/productSlice';
import ProductCard from './ProductCard';
import { BestSellersSkeleton, LoadingState } from '../common/ProductLoadingStates';
import { ProductsError, EmptyState } from '../common/ErrorStates';
import { FireIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

interface BestSellersSectionProps {
  limit?: number;
  showViewAll?: boolean;
  className?: string;
}

const BestSellersSection: React.FC<BestSellersSectionProps> = ({
  limit = 8,
  showViewAll = true,
  className = ''
}) => {
  const dispatch = useAppDispatch();
  const { bestSellerProducts, loading, error } = useAppSelector((state) => state.products);
  const [retryCount, setRetryCount] = useState(0);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const fetchData = async () => {
    try {
      await dispatch(fetchBestSellerProducts(limit)).unwrap();
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      console.error('Failed to fetch best sellers:', error);
    } finally {
      setIsInitialLoad(false);
    }
  };

  useEffect(() => {
    // Only fetch if we don't have products or if this is a retry
    if (bestSellerProducts.length === 0 || retryCount > 0) {
      fetchData();
    } else {
      setIsInitialLoad(false);
    }
  }, [dispatch, limit, retryCount]);

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    setIsInitialLoad(true);
  };

  const handleBrowseProducts = () => {
    // Navigate to shop page
    window.location.href = '/shop';
  };

  // Show skeleton loader on initial load
  if (isInitialLoad && loading) {
    return <BestSellersSkeleton />;
  }

  // Show error state if there's an error and no products
  if (error && bestSellerProducts.length === 0) {
    return (
      <section className={`py-16 bg-white ${className}`}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <FireIcon className="h-8 w-8 text-red-500 mr-3" />
              <h2 className="text-3xl font-bold text-gray-900">Best Sellers</h2>
            </div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Discover our most popular products loved by customers nationwide
            </p>
          </div>
          
          <ProductsError
            title="Unable to Load Best Sellers"
            message="We're having trouble loading our best-selling products right now. Please try again or browse our full catalog."
            onRetry={handleRetry}
            className="max-w-md mx-auto"
          />
        </div>
      </section>
    );
  }

  // Show empty state if no products are available
  if (!loading && bestSellerProducts.length === 0) {
    return (
      <section className={`py-16 bg-white ${className}`}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <FireIcon className="h-8 w-8 text-red-500 mr-3" />
              <h2 className="text-3xl font-bold text-gray-900">Best Sellers</h2>
            </div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Discover our most popular products loved by customers nationwide
            </p>
          </div>
          
          <EmptyState
            type="products"
            title="No Best Sellers Yet"
            message="We're working hard to bring you amazing products! Check back soon or browse our full catalog to discover what's new."
            actionText="Browse All Products"
            onAction={handleBrowseProducts}
            className="max-w-lg mx-auto"
          />
        </div>
      </section>
    );
  }

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <FireIcon className="h-8 w-8 text-red-500 mr-3 animate-pulse" />
            <h2 className="text-3xl font-bold text-gray-900">Best Sellers</h2>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover our most popular products loved by customers nationwide
          </p>
        </div>

        {/* Loading State for Additional Products */}
        {loading && bestSellerProducts.length > 0 && (
          <div className="text-center mb-8">
            <LoadingState 
              message="Loading more best sellers..." 
              size="small"
              className="py-4"
            />
          </div>
        )}

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {bestSellerProducts.slice(0, limit).map((product, index) => (
            <div key={product.id} className="relative">
              {/* Best Seller Badge for top 3 */}
              {index < 3 && (
                <div className="absolute top-2 left-2 z-10">
                  <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center">
                    <FireIcon className="h-3 w-3 mr-1" />
                    #{index + 1}
                  </div>
                </div>
              )}
              <ProductCard product={product} />
            </div>
          ))}
        </div>

        {/* View All Button */}
        {showViewAll && bestSellerProducts.length > 0 && (
          <div className="text-center">
            <Link
              to="/shop?bestSeller=true"
              className="inline-flex items-center px-8 py-4 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-lg"
            >
              View All Best Sellers
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </div>
        )}

        {/* Error Message for Partial Load */}
        {error && bestSellerProducts.length > 0 && (
          <div className="text-center mt-8">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
              <p className="text-yellow-800 text-sm">
                Some products couldn't be loaded. 
                <button 
                  onClick={handleRetry}
                  className="ml-2 text-yellow-600 hover:text-yellow-800 underline font-medium"
                >
                  Try again
                </button>
              </p>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default BestSellersSection;
