module.exports = {
  apps: [
    {
      name: 'nirvana-main-test',
      script: 'server/index.js',
      cwd: '/var/www/nirvana-test',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'test',
        PORT: 5000,
        PM2_SERVE_PATH: '/var/www/nirvana-test',
        PM2_SERVE_PORT: 5000,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/index.html'
      },
      env_test: {
        NODE_ENV: 'test',
        PORT: 5000
      },
      log_file: '/var/www/nirvana-test/logs/combined.log',
      out_file: '/var/www/nirvana-test/logs/out.log',
      error_file: '/var/www/nirvana-test/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      autorestart: true,
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        '.git'
      ],
      source_map_support: true,
      instance_var: 'INSTANCE_ID'
    },
    {
      name: 'nirvana-admin-test',
      script: 'server/admin-server.js',
      cwd: '/var/www/nirvana-test',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'test',
        PORT: 3001,
        PM2_SERVE_PATH: '/var/www/nirvana-test',
        PM2_SERVE_PORT: 3001,
        PM2_SERVE_SPA: 'true',
        PM2_SERVE_HOMEPAGE: '/admin.html'
      },
      env_test: {
        NODE_ENV: 'test',
        PORT: 3001
      },
      log_file: '/var/www/nirvana-test/logs/admin-combined.log',
      out_file: '/var/www/nirvana-test/logs/admin-out.log',
      error_file: '/var/www/nirvana-test/logs/admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      autorestart: true,
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        '.git'
      ],
      source_map_support: true,
      instance_var: 'INSTANCE_ID'
    }
  ]
};
