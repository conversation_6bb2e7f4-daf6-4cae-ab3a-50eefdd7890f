#!/bin/bash

# Nirvana Organics Production Environment Deployment Script
# This script deploys the complete production environment to /var/www/nirvana-production
# ONLY run this AFTER successful test deployment and validation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/var/www/nirvana-production"
FRONTEND_DIR="/var/www/nirvana-frontend/production"
NGINX_SITE="nirvana-production"
BACKUP_DIR="/var/backups/nirvana-production"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if service is running
service_running() {
    systemctl is-active --quiet "$1"
}

# Function to prompt for confirmation
confirm_deployment() {
    echo ""
    echo "🚨 PRODUCTION DEPLOYMENT WARNING 🚨"
    echo "======================================"
    echo ""
    echo "You are about to deploy to the PRODUCTION environment."
    echo "This will affect the live website at shopnirvanaorganics.com"
    echo ""
    echo "Prerequisites checklist:"
    echo "  ✓ Test environment deployed and validated"
    echo "  ✓ All functionality tested thoroughly"
    echo "  ✓ Database backups completed"
    echo "  ✓ SSL certificates configured"
    echo "  ✓ DNS pointing to this server"
    echo "  ✓ Environment variables configured"
    echo ""
    read -p "Are you sure you want to proceed? (type 'YES' to continue): " confirmation
    
    if [ "$confirmation" != "YES" ]; then
        print_error "Deployment cancelled by user"
        exit 1
    fi
}

# Function to create comprehensive backup
create_backup() {
    print_status "Creating comprehensive backup..."
    
    # Create backup directory with timestamp
    BACKUP_TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    CURRENT_BACKUP_DIR="$BACKUP_DIR/backup-$BACKUP_TIMESTAMP"
    sudo mkdir -p "$CURRENT_BACKUP_DIR"
    
    # Backup application files
    if [ -d "$PROJECT_DIR" ]; then
        print_status "Backing up application files..."
        sudo cp -r "$PROJECT_DIR" "$CURRENT_BACKUP_DIR/application"
    fi
    
    # Backup frontend files
    if [ -d "$FRONTEND_DIR" ]; then
        print_status "Backing up frontend files..."
        sudo cp -r "$FRONTEND_DIR" "$CURRENT_BACKUP_DIR/frontend"
    fi
    
    # Backup database
    print_status "Backing up database..."
    mysqldump -u root -p nirvana_organics_production > "$CURRENT_BACKUP_DIR/database.sql" 2>/dev/null || print_warning "Database backup failed - ensure database exists"
    
    # Backup Nginx configuration
    if [ -f "/etc/nginx/sites-available/$NGINX_SITE" ]; then
        sudo cp "/etc/nginx/sites-available/$NGINX_SITE" "$CURRENT_BACKUP_DIR/nginx.conf"
    fi
    
    # Backup PM2 configuration
    if [ -f "$PROJECT_DIR/ecosystem.config.production.js" ]; then
        sudo cp "$PROJECT_DIR/ecosystem.config.production.js" "$CURRENT_BACKUP_DIR/"
    fi
    
    print_success "Backup created at: $CURRENT_BACKUP_DIR"
}

# Function to install system dependencies
install_dependencies() {
    print_status "Installing system dependencies..."
    
    # Update package list
    sudo apt update
    
    # Install Node.js 18.x if not present
    if ! command_exists node || [ "$(node -v | cut -d'v' -f2 | cut -d'.' -f1)" -lt "18" ]; then
        print_status "Installing Node.js 18.x..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
    
    # Install other dependencies
    sudo apt-get install -y nginx mysql-server redis-server certbot python3-certbot-nginx git curl wget unzip fail2ban ufw
    
    # Install PM2 globally
    if ! command_exists pm2; then
        print_status "Installing PM2..."
        sudo npm install -g pm2
        sudo pm2 startup
    fi
    
    print_success "System dependencies installed"
}

# Function to setup MySQL for production
setup_mysql() {
    print_status "Setting up MySQL for production..."
    
    if ! service_running mysql; then
        sudo systemctl start mysql
        sudo systemctl enable mysql
    fi
    
    # Create production database user
    print_status "Creating production database user..."
    sudo mysql -e "CREATE USER IF NOT EXISTS 'nirvana_prod_user'@'localhost' IDENTIFIED BY 'CHANGE_THIS_SECURE_PRODUCTION_PASSWORD';" || true
    sudo mysql -e "GRANT ALL PRIVILEGES ON nirvana_organics_production.* TO 'nirvana_prod_user'@'localhost';" || true
    sudo mysql -e "FLUSH PRIVILEGES;" || true
    
    print_success "MySQL production setup completed"
}

# Function to setup Redis
setup_redis() {
    print_status "Setting up Redis..."
    
    if ! service_running redis-server; then
        sudo systemctl start redis-server
        sudo systemctl enable redis-server
    fi
    
    # Configure Redis for production
    sudo sed -i 's/# maxmemory <bytes>/maxmemory 256mb/' /etc/redis/redis.conf
    sudo sed -i 's/# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf
    sudo systemctl restart redis-server
    
    print_success "Redis setup completed"
}

# Function to setup firewall
setup_firewall() {
    print_status "Configuring firewall..."
    
    # Enable UFW
    sudo ufw --force enable
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80
    sudo ufw allow 443
    
    # Allow MySQL (only from localhost)
    sudo ufw allow from 127.0.0.1 to any port 3306
    
    # Allow Redis (only from localhost)
    sudo ufw allow from 127.0.0.1 to any port 6379
    
    print_success "Firewall configured"
}

# Function to create project directories
create_directories() {
    print_status "Creating project directories..."
    
    sudo mkdir -p "$PROJECT_DIR"
    sudo mkdir -p "$FRONTEND_DIR"
    sudo mkdir -p "$PROJECT_DIR/logs"
    sudo mkdir -p "$PROJECT_DIR/uploads"
    sudo mkdir -p "$PROJECT_DIR/uploads/products"
    sudo mkdir -p "$PROJECT_DIR/uploads/categories"
    sudo mkdir -p "$PROJECT_DIR/uploads/users"
    sudo mkdir -p "$BACKUP_DIR"
    
    # Set proper ownership
    sudo chown -R $USER:$USER "$PROJECT_DIR"
    sudo chown -R www-data:www-data "$FRONTEND_DIR"
    
    print_success "Directories created"
}

# Function to copy application files
copy_files() {
    print_status "Copying application files..."
    
    # Copy all files to project directory
    sudo cp -r ./* "$PROJECT_DIR/"
    sudo cp -r ./.env* "$PROJECT_DIR/" 2>/dev/null || true
    
    # Set proper ownership and permissions
    sudo chown -R $USER:$USER "$PROJECT_DIR"
    sudo chmod -R 755 "$PROJECT_DIR"
    sudo chmod 600 "$PROJECT_DIR"/.env*
    
    print_success "Application files copied"
}

# Function to install Node.js dependencies
install_node_dependencies() {
    print_status "Installing Node.js dependencies..."
    
    cd "$PROJECT_DIR"
    npm ci --production --silent
    
    print_success "Node.js dependencies installed"
}

# Function to build frontend
build_frontend() {
    print_status "Building frontend applications..."
    
    cd "$PROJECT_DIR"
    
    # Build main customer frontend
    print_status "Building customer frontend..."
    npm run build:prod
    
    # Build admin frontend
    print_status "Building admin frontend..."
    npm run build:admin:prod
    
    # Copy built files to frontend directory
    sudo cp -r dist/* "$FRONTEND_DIR/"
    sudo mkdir -p "$FRONTEND_DIR/admin"
    sudo cp -r dist-admin/* "$FRONTEND_DIR/admin/"
    
    # Set proper ownership
    sudo chown -R www-data:www-data "$FRONTEND_DIR"
    sudo chmod -R 755 "$FRONTEND_DIR"
    
    print_success "Frontend applications built and deployed"
}

# Function to setup database
setup_database() {
    print_status "Setting up production database..."
    
    cd "$PROJECT_DIR"
    NODE_ENV=production node scripts/setup-database.js
    
    print_success "Database setup completed"
}

# Function to configure Nginx
configure_nginx() {
    print_status "Configuring Nginx..."
    
    # Copy Nginx configuration
    sudo cp nginx-production.conf /etc/nginx/sites-available/$NGINX_SITE
    
    # Enable site
    sudo ln -sf /etc/nginx/sites-available/$NGINX_SITE /etc/nginx/sites-enabled/
    
    # Remove default site if it exists
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    sudo nginx -t
    
    # Reload Nginx
    sudo systemctl reload nginx
    sudo systemctl enable nginx
    
    print_success "Nginx configured"
}

# Function to setup SSL certificates
setup_ssl() {
    print_status "Setting up SSL certificates..."
    
    # Main domain certificate
    if [ ! -f "/etc/letsencrypt/live/shopnirvanaorganics.com/fullchain.pem" ]; then
        print_status "Obtaining SSL certificate for main domain..."
        sudo certbot --nginx -d shopnirvanaorganics.com -d www.shopnirvanaorganics.com --non-interactive --agree-tos --email <EMAIL>
    else
        print_warning "Main domain SSL certificate already exists"
    fi
    
    # Admin subdomain certificate
    if [ ! -f "/etc/letsencrypt/live/admin.shopnirvanaorganics.com/fullchain.pem" ]; then
        print_status "Obtaining SSL certificate for admin subdomain..."
        sudo certbot --nginx -d admin.shopnirvanaorganics.com --non-interactive --agree-tos --email <EMAIL>
    else
        print_warning "Admin subdomain SSL certificate already exists"
    fi
    
    # Setup auto-renewal
    sudo systemctl enable certbot.timer
    
    print_success "SSL certificates configured"
}

# Function to start PM2 processes
start_pm2() {
    print_status "Starting PM2 processes..."
    
    cd "$PROJECT_DIR"
    
    # Stop existing processes
    pm2 delete all 2>/dev/null || true
    
    # Start new processes
    pm2 start ecosystem.config.production.js --env production
    
    # Save PM2 configuration
    pm2 save
    
    print_success "PM2 processes started"
}

# Function to verify deployment
verify_deployment() {
    print_status "Verifying production deployment..."
    
    # Check if processes are running
    if pm2 list | grep -q "online"; then
        print_success "PM2 processes are running"
    else
        print_error "PM2 processes are not running properly"
        return 1
    fi
    
    # Check if services are running
    for service in nginx mysql redis-server; do
        if service_running $service; then
            print_success "$service is running"
        else
            print_error "$service is not running"
            return 1
        fi
    done
    
    # Test HTTP responses
    sleep 10  # Wait for services to fully start
    
    if curl -f -s http://localhost:5000/health > /dev/null; then
        print_success "Main server health check passed"
    else
        print_warning "Main server health check failed"
    fi
    
    if curl -f -s http://localhost:3001/health > /dev/null; then
        print_success "Admin server health check passed"
    else
        print_warning "Admin server health check failed"
    fi
    
    print_success "Production deployment verification completed"
}

# Main deployment function
main() {
    echo "🚀 Starting Nirvana Organics Production Environment Deployment"
    echo "=============================================================="
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_error "Please do not run this script as root"
        exit 1
    fi
    
    # Confirm deployment
    confirm_deployment
    
    # Create comprehensive backup
    create_backup
    
    # Install dependencies
    install_dependencies
    
    # Setup services
    setup_mysql
    setup_redis
    setup_firewall
    
    # Create directories
    create_directories
    
    # Copy files
    copy_files
    
    # Install Node.js dependencies
    install_node_dependencies
    
    # Build frontend
    build_frontend
    
    # Setup database
    setup_database
    
    # Configure Nginx
    configure_nginx
    
    # Setup SSL certificates
    setup_ssl
    
    # Start PM2 processes
    start_pm2
    
    # Verify deployment
    verify_deployment
    
    echo ""
    echo "🎉 Production Environment Deployment Completed Successfully!"
    echo "==========================================================="
    echo ""
    echo "📋 Production Deployment Summary:"
    echo "   • Project Directory: $PROJECT_DIR"
    echo "   • Frontend Directory: $FRONTEND_DIR"
    echo "   • Main Website: https://shopnirvanaorganics.com"
    echo "   • Admin Panel: https://admin.shopnirvanaorganics.com"
    echo "   • Backup Location: $CURRENT_BACKUP_DIR"
    echo ""
    echo "🔑 Important Security Notes:"
    echo "   • Change all default passwords immediately"
    echo "   • Update environment variables with production values"
    echo "   • Configure payment gateways with live credentials"
    echo "   • Set up monitoring and alerting"
    echo ""
    echo "📝 Post-Deployment Tasks:"
    echo "   1. Update DNS records if needed"
    echo "   2. Configure payment gateways"
    echo "   3. Set up monitoring and backups"
    echo "   4. Test all critical functionality"
    echo "   5. Configure email services"
    echo "   6. Set up analytics and tracking"
    echo ""
    echo "🔧 Useful Commands:"
    echo "   • View logs: pm2 logs"
    echo "   • Restart services: pm2 restart all"
    echo "   • Check status: pm2 status"
    echo "   • Nginx logs: sudo tail -f /var/log/nginx/nirvana-prod-error.log"
    echo "   • System status: systemctl status nginx mysql redis-server"
    echo ""
    echo "🚨 Remember: This is now LIVE and serving real customers!"
    echo ""
}

# Run main function
main "$@"
