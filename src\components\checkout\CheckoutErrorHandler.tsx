import React from 'react';
import { PaymentError, AuthError, NetworkError } from '../common/ErrorStates';
import { ExclamationTriangleIcon, CreditCardIcon, UserIcon, WifiIcon } from '@heroicons/react/24/outline';

interface CheckoutErrorHandlerProps {
  error: string | null;
  step: 'shipping' | 'payment' | 'confirmation';
  onRetry?: () => void;
  onGoBack?: () => void;
  className?: string;
}

const CheckoutErrorHandler: React.FC<CheckoutErrorHandlerProps> = ({
  error,
  step,
  onRetry,
  onGoBack,
  className = ''
}) => {
  if (!error) return null;

  const getErrorType = (errorMessage: string, currentStep: string) => {
    const lowerError = errorMessage.toLowerCase();
    
    // Network errors
    if (lowerError.includes('network') || lowerError.includes('connection') || lowerError.includes('timeout')) {
      return 'network';
    }
    
    // Authentication errors
    if (lowerError.includes('auth') || lowerError.includes('login') || lowerError.includes('unauthorized')) {
      return 'auth';
    }
    
    // Payment specific errors
    if (currentStep === 'payment' || lowerError.includes('payment') || lowerError.includes('card') || lowerError.includes('declined')) {
      return 'payment';
    }
    
    return 'general';
  };

  const errorType = getErrorType(error, step);

  const getErrorConfig = () => {
    switch (errorType) {
      case 'network':
        return {
          title: 'Connection Problem',
          message: 'Unable to process your request due to a connection issue. Please check your internet connection and try again.',
          icon: WifiIcon,
          color: 'red'
        };
      case 'auth':
        return {
          title: 'Authentication Required',
          message: 'Please log in to continue with your checkout process.',
          icon: UserIcon,
          color: 'purple'
        };
      case 'payment':
        return {
          title: 'Payment Error',
          message: getPaymentErrorMessage(error),
          icon: CreditCardIcon,
          color: 'red'
        };
      default:
        return {
          title: 'Checkout Error',
          message: error,
          icon: ExclamationTriangleIcon,
          color: 'orange'
        };
    }
  };

  const getPaymentErrorMessage = (errorMessage: string) => {
    const lowerError = errorMessage.toLowerCase();
    
    if (lowerError.includes('declined') || lowerError.includes('insufficient')) {
      return 'Your payment was declined. Please check your payment details or try a different payment method.';
    }
    if (lowerError.includes('expired')) {
      return 'Your payment method has expired. Please update your payment information.';
    }
    if (lowerError.includes('invalid')) {
      return 'The payment information provided is invalid. Please check your details and try again.';
    }
    if (lowerError.includes('limit')) {
      return 'Your payment has exceeded the limit. Please contact your bank or try a different payment method.';
    }
    
    return errorMessage;
  };

  const config = getErrorConfig();
  const IconComponent = config.icon;

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'red':
        return {
          bg: 'bg-red-50',
          border: 'border-red-200',
          icon: 'text-red-500',
          title: 'text-red-800',
          message: 'text-red-700'
        };
      case 'purple':
        return {
          bg: 'bg-purple-50',
          border: 'border-purple-200',
          icon: 'text-purple-500',
          title: 'text-purple-800',
          message: 'text-purple-700'
        };
      case 'orange':
        return {
          bg: 'bg-orange-50',
          border: 'border-orange-200',
          icon: 'text-orange-500',
          title: 'text-orange-800',
          message: 'text-orange-700'
        };
      default:
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          icon: 'text-gray-500',
          title: 'text-gray-800',
          message: 'text-gray-700'
        };
    }
  };

  const colors = getColorClasses(config.color);

  return (
    <div className={`rounded-lg border-2 ${colors.border} ${colors.bg} p-6 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <IconComponent className={`h-6 w-6 ${colors.icon}`} />
        </div>
        <div className="ml-3 flex-1">
          <h3 className={`text-lg font-semibold ${colors.title} mb-2`}>
            {config.title}
          </h3>
          <p className={`text-sm ${colors.message} mb-4 leading-relaxed`}>
            {config.message}
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3">
            {onRetry && (
              <button
                onClick={onRetry}
                className="inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                Try Again
              </button>
            )}
            
            {onGoBack && (
              <button
                onClick={onGoBack}
                className="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-800 font-medium rounded-lg hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Go Back
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Specific checkout error components
export const ShippingError: React.FC<Omit<CheckoutErrorHandlerProps, 'step'>> = (props) => (
  <CheckoutErrorHandler step="shipping" {...props} />
);

export const PaymentErrorComponent: React.FC<Omit<CheckoutErrorHandlerProps, 'step'>> = (props) => (
  <CheckoutErrorHandler step="payment" {...props} />
);

export const ConfirmationError: React.FC<Omit<CheckoutErrorHandlerProps, 'step'>> = (props) => (
  <CheckoutErrorHandler step="confirmation" {...props} />
);

// Form validation error display
interface FormErrorProps {
  errors: Record<string, string>;
  className?: string;
}

export const FormErrors: React.FC<FormErrorProps> = ({ errors, className = '' }) => {
  const errorEntries = Object.entries(errors);
  
  if (errorEntries.length === 0) return null;

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex">
        <ExclamationTriangleIcon className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800 mb-2">
            Please correct the following errors:
          </h3>
          <ul className="text-sm text-red-700 space-y-1">
            {errorEntries.map(([field, message]) => (
              <li key={field} className="flex items-start">
                <span className="inline-block w-2 h-2 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                <span className="capitalize">{field.replace(/([A-Z])/g, ' $1').toLowerCase()}: {message}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CheckoutErrorHandler;
