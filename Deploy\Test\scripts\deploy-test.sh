#!/bin/bash

# Nirvana Organics Test Environment Deployment Script
# This script deploys the complete test environment to /var/www/nirvana-test

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/var/www/nirvana-test"
FRONTEND_DIR="/var/www/nirvana-frontend/test"
NGINX_SITE="nirvana-test"
BACKUP_DIR="/var/backups/nirvana-test"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if service is running
service_running() {
    systemctl is-active --quiet "$1"
}

# Function to create backup
create_backup() {
    if [ -d "$PROJECT_DIR" ]; then
        print_status "Creating backup of existing installation..."
        sudo mkdir -p "$BACKUP_DIR"
        sudo cp -r "$PROJECT_DIR" "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
        print_success "Backup created successfully"
    fi
}

# Function to install system dependencies
install_dependencies() {
    print_status "Installing system dependencies..."
    
    # Update package list
    sudo apt update
    
    # Install Node.js 18.x if not present
    if ! command_exists node || [ "$(node -v | cut -d'v' -f2 | cut -d'.' -f1)" -lt "18" ]; then
        print_status "Installing Node.js 18.x..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
    
    # Install other dependencies
    sudo apt-get install -y nginx mysql-server certbot python3-certbot-nginx git curl wget unzip
    
    # Install PM2 globally
    if ! command_exists pm2; then
        print_status "Installing PM2..."
        sudo npm install -g pm2
        sudo pm2 startup
    fi
    
    print_success "System dependencies installed"
}

# Function to setup MySQL
setup_mysql() {
    print_status "Setting up MySQL..."
    
    if ! service_running mysql; then
        sudo systemctl start mysql
        sudo systemctl enable mysql
    fi
    
    # Secure MySQL installation (basic setup)
    sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'NirvanaTest123!';" || true
    sudo mysql -u root -pNirvanaTest123! -e "DELETE FROM mysql.user WHERE User='';" || true
    sudo mysql -u root -pNirvanaTest123! -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');" || true
    sudo mysql -u root -pNirvanaTest123! -e "DROP DATABASE IF EXISTS test;" || true
    sudo mysql -u root -pNirvanaTest123! -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';" || true
    sudo mysql -u root -pNirvanaTest123! -e "FLUSH PRIVILEGES;" || true
    
    print_success "MySQL setup completed"
}

# Function to create project directories
create_directories() {
    print_status "Creating project directories..."
    
    sudo mkdir -p "$PROJECT_DIR"
    sudo mkdir -p "$FRONTEND_DIR"
    sudo mkdir -p "$PROJECT_DIR/logs"
    sudo mkdir -p "$PROJECT_DIR/uploads"
    sudo mkdir -p "$PROJECT_DIR/uploads/products"
    sudo mkdir -p "$PROJECT_DIR/uploads/categories"
    sudo mkdir -p "$PROJECT_DIR/uploads/users"
    
    # Set proper ownership
    sudo chown -R $USER:$USER "$PROJECT_DIR"
    sudo chown -R www-data:www-data "$FRONTEND_DIR"
    
    print_success "Directories created"
}

# Function to copy application files
copy_files() {
    print_status "Copying application files..."
    
    # Copy all files to project directory
    sudo cp -r ./* "$PROJECT_DIR/"
    sudo cp -r ./.env* "$PROJECT_DIR/" 2>/dev/null || true
    
    # Set proper ownership
    sudo chown -R $USER:$USER "$PROJECT_DIR"
    
    print_success "Application files copied"
}

# Function to install Node.js dependencies
install_node_dependencies() {
    print_status "Installing Node.js dependencies..."
    
    cd "$PROJECT_DIR"
    npm ci --production
    
    print_success "Node.js dependencies installed"
}

# Function to build frontend
build_frontend() {
    print_status "Building frontend applications..."
    
    cd "$PROJECT_DIR"
    
    # Build main customer frontend
    print_status "Building customer frontend..."
    npm run build:test
    
    # Build admin frontend
    print_status "Building admin frontend..."
    npm run build:admin:test
    
    # Copy built files to frontend directory
    sudo cp -r dist/* "$FRONTEND_DIR/"
    sudo cp -r dist-admin/* "$FRONTEND_DIR/admin/"
    
    # Set proper ownership
    sudo chown -R www-data:www-data "$FRONTEND_DIR"
    
    print_success "Frontend applications built and deployed"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    cd "$PROJECT_DIR"
    node scripts/setup-database.js
    
    print_success "Database setup completed"
}

# Function to configure Nginx
configure_nginx() {
    print_status "Configuring Nginx..."
    
    # Copy Nginx configuration
    sudo cp nginx-test.conf /etc/nginx/sites-available/$NGINX_SITE
    
    # Enable site
    sudo ln -sf /etc/nginx/sites-available/$NGINX_SITE /etc/nginx/sites-enabled/
    
    # Remove default site if it exists
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    sudo nginx -t
    
    # Reload Nginx
    sudo systemctl reload nginx
    sudo systemctl enable nginx
    
    print_success "Nginx configured"
}

# Function to setup SSL certificate
setup_ssl() {
    print_status "Setting up SSL certificate..."
    
    # Check if certificate already exists
    if [ ! -f "/etc/letsencrypt/live/test.shopnirvanaorganics.com/fullchain.pem" ]; then
        print_status "Obtaining SSL certificate..."
        sudo certbot --nginx -d test.shopnirvanaorganics.com --non-interactive --agree-tos --email <EMAIL>
    else
        print_warning "SSL certificate already exists"
    fi
    
    # Setup auto-renewal
    sudo systemctl enable certbot.timer
    
    print_success "SSL certificate configured"
}

# Function to start PM2 processes
start_pm2() {
    print_status "Starting PM2 processes..."
    
    cd "$PROJECT_DIR"
    
    # Stop existing processes
    pm2 delete all 2>/dev/null || true
    
    # Start new processes
    pm2 start ecosystem.config.test.js --env test
    
    # Save PM2 configuration
    pm2 save
    
    print_success "PM2 processes started"
}

# Function to verify deployment
verify_deployment() {
    print_status "Verifying deployment..."
    
    # Check if processes are running
    if pm2 list | grep -q "online"; then
        print_success "PM2 processes are running"
    else
        print_error "PM2 processes are not running properly"
        return 1
    fi
    
    # Check if Nginx is running
    if service_running nginx; then
        print_success "Nginx is running"
    else
        print_error "Nginx is not running"
        return 1
    fi
    
    # Check if MySQL is running
    if service_running mysql; then
        print_success "MySQL is running"
    else
        print_error "MySQL is not running"
        return 1
    fi
    
    # Test HTTP response
    sleep 5  # Wait for services to fully start
    if curl -f -s http://localhost:5000/health > /dev/null; then
        print_success "Main server health check passed"
    else
        print_warning "Main server health check failed"
    fi
    
    if curl -f -s http://localhost:3001/health > /dev/null; then
        print_success "Admin server health check passed"
    else
        print_warning "Admin server health check failed"
    fi
    
    print_success "Deployment verification completed"
}

# Main deployment function
main() {
    echo "🚀 Starting Nirvana Organics Test Environment Deployment"
    echo "========================================================"
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_error "Please do not run this script as root"
        exit 1
    fi
    
    # Create backup
    create_backup
    
    # Install dependencies
    install_dependencies
    
    # Setup MySQL
    setup_mysql
    
    # Create directories
    create_directories
    
    # Copy files
    copy_files
    
    # Install Node.js dependencies
    install_node_dependencies
    
    # Build frontend
    build_frontend
    
    # Setup database
    setup_database
    
    # Configure Nginx
    configure_nginx
    
    # Setup SSL (optional, may fail if domain not pointing to server)
    setup_ssl || print_warning "SSL setup failed - you may need to configure DNS first"
    
    # Start PM2 processes
    start_pm2
    
    # Verify deployment
    verify_deployment
    
    echo ""
    echo "🎉 Test Environment Deployment Completed Successfully!"
    echo "====================================================="
    echo ""
    echo "📋 Deployment Summary:"
    echo "   • Project Directory: $PROJECT_DIR"
    echo "   • Frontend Directory: $FRONTEND_DIR"
    echo "   • Main Server: http://localhost:5000"
    echo "   • Admin Server: http://localhost:3001"
    echo "   • Website: https://test.shopnirvanaorganics.com"
    echo "   • Admin Panel: https://test.shopnirvanaorganics.com/admin"
    echo ""
    echo "🔑 Default Admin Credentials:"
    echo "   • Email: <EMAIL>"
    echo "   • Password: Admin123!"
    echo ""
    echo "📝 Next Steps:"
    echo "   1. Point your domain DNS to this server"
    echo "   2. Update environment variables in .env.test"
    echo "   3. Change default admin password"
    echo "   4. Configure payment gateways"
    echo "   5. Test all functionality"
    echo ""
    echo "🔧 Useful Commands:"
    echo "   • View logs: pm2 logs"
    echo "   • Restart services: pm2 restart all"
    echo "   • Check status: pm2 status"
    echo "   • Nginx logs: sudo tail -f /var/log/nginx/nirvana-test-error.log"
    echo ""
}

# Run main function
main "$@"
