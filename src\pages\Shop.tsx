import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchProducts, setFilters, clearFilters } from '../store/slices/productSlice';
import type { ProductFilters } from '../types';
import ProductCard from '../components/products/ProductCard';
import { default as ProductFiltersComponent } from '../components/products/ProductFilters';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { ProductGridSkeleton, LoadingState } from '../components/common/ProductLoadingStates';
import { ProductsError, EmptyState, NetworkError } from '../components/common/ErrorStates';
import SEOHead from '../components/seo/SEOHead';
import Breadcrumb, { BreadcrumbItem } from '../components/common/Breadcrumb';
import { ChevronDownIcon, FunnelIcon } from '@heroicons/react/24/outline';

const Shop: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [showFilters, setShowFilters] = useState(false);
  const dispatch = useAppDispatch();

  const { products, loading, error, pagination, filters } = useAppSelector((state) => state.products);

  // Initialize filters from URL params
  useEffect(() => {
    const urlFilters: Partial<ProductFilters> = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '12'),
      category: searchParams.get('category') || undefined,
      subcategory: searchParams.get('subcategory') || undefined,
      cannabinoid: searchParams.get('cannabinoid') || undefined,
      strain: searchParams.get('strain') || undefined,
      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      inStock: searchParams.get('inStock') === 'true' || undefined,
      featured: searchParams.get('featured') === 'true' || undefined,
      bestSeller: searchParams.get('bestSeller') === 'true' || undefined,
      search: searchParams.get('search') || undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
    };

    dispatch(setFilters(urlFilters));
  }, [searchParams, dispatch]);

  // Fetch products when filters change
  useEffect(() => {
    dispatch(fetchProducts(filters));
  }, [dispatch, filters]);

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });
    setSearchParams(params);
  }, [filters, setSearchParams]);

  const handleFilterChange = (newFilters: Partial<ProductFilters>) => {
    dispatch(setFilters({ ...newFilters, page: 1 }));
  };

  const handleSortChange = (sortBy: string, sortOrder: 'asc' | 'desc') => {
    dispatch(setFilters({ sortBy, sortOrder, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    dispatch(setFilters({ page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const clearAllFilters = () => {
    dispatch(clearFilters());
    setSearchParams({});
  };

  const sortOptions = [
    { value: 'createdAt-desc', label: 'Newest First' },
    { value: 'createdAt-asc', label: 'Oldest First' },
    { value: 'price-asc', label: 'Price: Low to High' },
    { value: 'price-desc', label: 'Price: High to Low' },
    { value: 'name-asc', label: 'Name: A to Z' },
    { value: 'name-desc', label: 'Name: Z to A' },
    { value: 'averageRating-desc', label: 'Highest Rated' },
    { value: 'salesCount-desc', label: 'Best Selling' }
  ];

  const currentSort = `${filters.sortBy}-${filters.sortOrder}`;

  // Generate dynamic SEO content based on filters
  const generateSEOContent = () => {
    const searchQuery = searchParams.get('search');
    const category = searchParams.get('category');
    const cannabinoid = searchParams.get('cannabinoid');
    const featured = searchParams.get('featured');
    const bestSeller = searchParams.get('bestSeller');

    let title = 'Shop Premium Cannabis Products';
    let description = 'Browse our extensive collection of premium hemp-derived cannabis products including flowers, edibles, pre-rolls, and more. Lab-tested, legal, and delivered nationwide.';
    let keywords = ['cannabis products', 'hemp products', 'CBD', 'THC-A', 'Delta-8', 'Delta-9', 'cannabis shop', 'hemp store'];

    if (searchQuery) {
      title = `Search Results for "${searchQuery}" - Cannabis Products`;
      description = `Find cannabis products matching "${searchQuery}". Browse flowers, edibles, pre-rolls, and more premium hemp-derived products.`;
      keywords = [...keywords, searchQuery, `${searchQuery} cannabis`, `${searchQuery} hemp`];
    } else if (featured === 'true') {
      title = 'Featured Cannabis Products - Premium Hemp Collection';
      description = 'Discover our featured selection of premium cannabis products. Hand-picked flowers, edibles, pre-rolls, and concentrates from top brands.';
      keywords = [...keywords, 'featured cannabis', 'premium cannabis', 'top cannabis products'];
    } else if (bestSeller === 'true') {
      title = 'Best Selling Cannabis Products - Customer Favorites';
      description = 'Shop our best-selling cannabis products loved by customers. Popular flowers, edibles, pre-rolls, and concentrates with proven quality.';
      keywords = [...keywords, 'best selling cannabis', 'popular cannabis', 'top rated cannabis'];
    } else if (cannabinoid) {
      title = `${cannabinoid} Cannabis Products - Premium ${cannabinoid} Collection`;
      description = `Shop premium ${cannabinoid} cannabis products. High-quality ${cannabinoid} flowers, edibles, pre-rolls, and concentrates. Lab-tested and legal.`;
      keywords = [...keywords, `${cannabinoid} products`, `${cannabinoid} cannabis`, `${cannabinoid} hemp`];
    } else if (category) {
      title = `${category} Cannabis Products - Premium ${category} Collection`;
      description = `Browse our ${category} cannabis product collection. Premium quality ${category} with lab testing and nationwide shipping.`;
      keywords = [...keywords, `${category} cannabis`, `cannabis ${category}`, `hemp ${category}`];
    }

    return { title, description, keywords };
  };

  const seoContent = generateSEOContent();

  // Generate breadcrumbs based on current filters
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Shop', href: '/shop' }
    ];

    const searchQuery = searchParams.get('search');
    const category = searchParams.get('category');
    const cannabinoid = searchParams.get('cannabinoid');
    const featured = searchParams.get('featured');
    const bestSeller = searchParams.get('bestSeller');

    if (searchQuery) {
      breadcrumbs.push({ label: `Search: "${searchQuery}"`, current: true });
    } else if (featured === 'true') {
      breadcrumbs.push({ label: 'Featured Products', current: true });
    } else if (bestSeller === 'true') {
      breadcrumbs.push({ label: 'Best Sellers', current: true });
    } else if (cannabinoid) {
      breadcrumbs.push({ label: `${cannabinoid} Products`, current: true });
    } else if (category) {
      breadcrumbs.push({ label: category, current: true });
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Determine error type for better user experience
  const getErrorType = (errorMessage: string) => {
    if (errorMessage.toLowerCase().includes('network') || errorMessage.toLowerCase().includes('connection')) {
      return 'network';
    }
    if (errorMessage.toLowerCase().includes('server') || errorMessage.toLowerCase().includes('500')) {
      return 'server';
    }
    return 'products';
  };

  if (error && products.length === 0) {
    const errorType = getErrorType(error);

    return (
      <div className="container mx-auto px-4 py-8">
        <Breadcrumb items={generateBreadcrumbs()} />

        <div className="mt-8">
          {errorType === 'network' ? (
            <NetworkError
              title="Connection Problem"
              message="Unable to load products. Please check your internet connection and try again."
              onRetry={() => dispatch(fetchProducts(filters))}
              className="max-w-md mx-auto"
            />
          ) : (
            <ProductsError
              title="Unable to Load Products"
              message={error}
              onRetry={() => dispatch(fetchProducts(filters))}
              className="max-w-md mx-auto"
            />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <SEOHead
        title={seoContent.title}
        description={seoContent.description}
        keywords={seoContent.keywords}
        canonicalUrl="/shop"
        ogType="website"
      />
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbs} className="mb-6" />

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Shop Cannabis Products</h1>
          <p className="text-gray-600">
            Discover our premium selection of hemp-derived cannabis products
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-1/4">
            <div className="lg:hidden mb-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center justify-between w-full p-3 bg-white border border-gray-300 rounded-lg"
              >
                <span className="flex items-center">
                  <FunnelIcon className="h-5 w-5 mr-2" />
                  Filters
                </span>
                <ChevronDownIcon
                  className={`h-5 w-5 transform transition-transform ${showFilters ? 'rotate-180' : ''}`}
                />
              </button>
            </div>

            <div className={`${showFilters ? 'block' : 'hidden'} lg:block`}>
              <ProductFiltersComponent
                filters={filters}
                onFilterChange={handleFilterChange}
                onClearFilters={clearAllFilters}
              />
            </div>
          </div>

          {/* Products Grid */}
          <div className="lg:w-3/4">
            {/* Sort and Results Info */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
              <div className="text-sm text-gray-600">
                {pagination && pagination.total > 0 ? (
                  <>
                    Showing {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} products
                  </>
                ) : (
                  'No products found'
                )}
              </div>

              <div className="flex items-center gap-2">
                <label htmlFor="sort" className="text-sm font-medium text-gray-700">
                  Sort by:
                </label>
                <select
                  id="sort"
                  value={currentSort}
                  onChange={(e) => {
                    const [sortBy, sortOrder] = e.target.value.split('-');
                    handleSortChange(sortBy, sortOrder as 'asc' | 'desc');
                  }}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Loading State */}
            {loading && products.length === 0 && (
              <ProductGridSkeleton count={12} columns={4} className="mt-8" />
            )}

            {/* Loading More Products */}
            {loading && products.length > 0 && (
              <div className="text-center py-8">
                <LoadingState
                  message="Loading more products..."
                  size="medium"
                />
              </div>
            )}

            {/* Products Grid */}
            {!loading && products.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {products.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            )}

            {/* No Products Found */}
            {!loading && products.length === 0 && !error && (
              <EmptyState
                type="search"
                title="No products found"
                message="We couldn't find any products matching your criteria. Try adjusting your filters or search terms."
                actionText="Clear All Filters"
                onAction={clearAllFilters}
                className="py-12"
              />
            )}

            {/* Pagination */}
            {!loading && products.length > 0 && pagination && pagination.pages > 1 && (
              <div className="flex justify-center">
                <nav className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange((pagination?.page || 1) - 1)}
                    disabled={(pagination?.page || 1) <= 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  {Array.from({ length: Math.min(5, pagination?.pages || 1) }, (_, i) => {
                    const currentPage = pagination?.page || 1;
                    const totalPages = pagination?.pages || 1;
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          pageNum === (pagination?.page || 1)
                            ? 'bg-primary-600 text-white'
                            : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => handlePageChange((pagination?.page || 1) + 1)}
                    disabled={(pagination?.page || 1) >= (pagination?.pages || 1)}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </nav>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Shop;
