#!/usr/bin/env node

/**
 * Database Setup Script for Nirvana Organics Test Environment
 * This script creates the database, runs migrations, and seeds initial data
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.test' });

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'nirvana_organics_test'
};

const ADMIN_CONFIG = {
  host: process.env.ADMIN_DB_HOST || 'localhost',
  port: process.env.ADMIN_DB_PORT || 3306,
  user: process.env.ADMIN_DB_USER || 'root',
  password: process.env.ADMIN_DB_PASSWORD,
  database: process.env.ADMIN_DB_NAME || 'nirvana_organics_admin_test'
};

async function createDatabase(config, dbName) {
  console.log(`🔧 Creating database: ${dbName}`);
  
  const connection = await mysql.createConnection({
    host: config.host,
    port: config.port,
    user: config.user,
    password: config.password
  });

  try {
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ Database ${dbName} created successfully`);
  } catch (error) {
    console.error(`❌ Error creating database ${dbName}:`, error.message);
    throw error;
  } finally {
    await connection.end();
  }
}

async function testConnection(config) {
  console.log(`🔍 Testing connection to ${config.database}`);
  
  try {
    const connection = await mysql.createConnection(config);
    await connection.execute('SELECT 1');
    await connection.end();
    console.log(`✅ Connection to ${config.database} successful`);
    return true;
  } catch (error) {
    console.error(`❌ Connection to ${config.database} failed:`, error.message);
    return false;
  }
}

async function runMigrations() {
  console.log('🔄 Running database migrations...');
  
  try {
    const { sequelize } = require('../server/models');
    await sequelize.authenticate();
    console.log('✅ Database connection established');
    
    // Sync all models (create tables)
    await sequelize.sync({ force: false, alter: true });
    console.log('✅ Database tables synchronized');
    
    await sequelize.close();
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    throw error;
  }
}

async function seedDatabase() {
  console.log('🌱 Seeding database with initial data...');
  
  try {
    const { sequelize, User, Role, Category, Product } = require('../server/models');
    
    // Create default roles
    const adminRole = await Role.findOrCreate({
      where: { name: 'admin' },
      defaults: {
        name: 'admin',
        description: 'Administrator role with full access',
        permissions: JSON.stringify(['*'])
      }
    });

    const userRole = await Role.findOrCreate({
      where: { name: 'user' },
      defaults: {
        name: 'user',
        description: 'Regular user role',
        permissions: JSON.stringify(['read:products', 'create:orders', 'read:profile', 'update:profile'])
      }
    });

    // Create default admin user
    const bcrypt = require('bcryptjs');
    const adminPassword = await bcrypt.hash('Admin123!', 12);
    
    const adminUser = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: adminPassword,
        isEmailVerified: true,
        roleId: adminRole[0].id,
        isActive: true
      }
    });

    // Create default categories
    const categories = [
      { name: 'CBD Products', description: 'High-quality CBD products', slug: 'cbd-products' },
      { name: 'THC-A Products', description: 'Premium THC-A products', slug: 'thc-a-products' },
      { name: 'Edibles', description: 'Delicious cannabis edibles', slug: 'edibles' },
      { name: 'Topicals', description: 'Cannabis-infused topical products', slug: 'topicals' },
      { name: 'Accessories', description: 'Cannabis accessories and tools', slug: 'accessories' }
    ];

    for (const categoryData of categories) {
      await Category.findOrCreate({
        where: { slug: categoryData.slug },
        defaults: categoryData
      });
    }

    console.log('✅ Database seeded successfully');
    await sequelize.close();
  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
    throw error;
  }
}

async function createLogDirectories() {
  console.log('📁 Creating log directories...');
  
  const logDirs = [
    'logs',
    'server/logs',
    'uploads',
    'uploads/products',
    'uploads/categories',
    'uploads/users'
  ];

  for (const dir of logDirs) {
    try {
      await fs.mkdir(dir, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);
    } catch (error) {
      if (error.code !== 'EEXIST') {
        console.error(`❌ Failed to create directory ${dir}:`, error.message);
      }
    }
  }
}

async function validateEnvironment() {
  console.log('🔍 Validating environment configuration...');
  
  const requiredVars = [
    'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
    'JWT_SECRET', 'SESSION_SECRET'
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:', missing.join(', '));
    throw new Error('Environment validation failed');
  }
  
  console.log('✅ Environment validation passed');
}

async function main() {
  console.log('🚀 Starting Nirvana Organics Test Database Setup');
  console.log('================================================');
  
  try {
    // Validate environment
    await validateEnvironment();
    
    // Create log directories
    await createLogDirectories();
    
    // Create databases
    await createDatabase(DB_CONFIG, DB_CONFIG.database);
    await createDatabase(ADMIN_CONFIG, ADMIN_CONFIG.database);
    
    // Test connections
    const mainDbOk = await testConnection(DB_CONFIG);
    const adminDbOk = await testConnection(ADMIN_CONFIG);
    
    if (!mainDbOk || !adminDbOk) {
      throw new Error('Database connection tests failed');
    }
    
    // Run migrations
    await runMigrations();
    
    // Seed database
    await seedDatabase();
    
    console.log('');
    console.log('🎉 Database setup completed successfully!');
    console.log('');
    console.log('📋 Setup Summary:');
    console.log(`   • Main Database: ${DB_CONFIG.database}`);
    console.log(`   • Admin Database: ${ADMIN_CONFIG.database}`);
    console.log('   • Tables created and synchronized');
    console.log('   • Initial data seeded');
    console.log('   • Default admin user created: <EMAIL>');
    console.log('   • Default password: Admin123!');
    console.log('');
    console.log('⚠️  Remember to change the default admin password after first login!');
    
  } catch (error) {
    console.error('');
    console.error('💥 Database setup failed:', error.message);
    console.error('');
    process.exit(1);
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { main, createDatabase, testConnection, runMigrations, seedDatabase };
