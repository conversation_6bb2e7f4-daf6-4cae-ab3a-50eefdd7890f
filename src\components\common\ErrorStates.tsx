import React from 'react';
import { ExclamationTriangleIcon, WifiIcon, ServerIcon, ShoppingBagIcon, UserIcon, CreditCardIcon } from '@heroicons/react/24/outline';

interface ErrorStateProps {
  type: 'network' | 'server' | 'products' | 'auth' | 'payment' | 'general';
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
  className?: string;
}

const ErrorStates: React.FC<ErrorStateProps> = ({
  type,
  title,
  message,
  onRetry,
  showRetryButton = true,
  className = ''
}) => {
  const getErrorConfig = () => {
    switch (type) {
      case 'network':
        return {
          icon: WifiIcon,
          defaultTitle: 'Connection Problem',
          defaultMessage: 'Unable to connect to our servers. Please check your internet connection and try again.',
          iconColor: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        };
      case 'server':
        return {
          icon: ServerIcon,
          defaultTitle: 'Server Error',
          defaultMessage: 'Our servers are experiencing issues. Please try again in a few moments.',
          iconColor: 'text-orange-500',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200'
        };
      case 'products':
        return {
          icon: ShoppingBagIcon,
          defaultTitle: 'Products Unavailable',
          defaultMessage: 'We\'re having trouble loading products right now. Please try again or browse our categories.',
          iconColor: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        };
      case 'auth':
        return {
          icon: UserIcon,
          defaultTitle: 'Authentication Error',
          defaultMessage: 'There was a problem with your login. Please check your credentials and try again.',
          iconColor: 'text-purple-500',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200'
        };
      case 'payment':
        return {
          icon: CreditCardIcon,
          defaultTitle: 'Payment Error',
          defaultMessage: 'There was an issue processing your payment. Please check your payment details and try again.',
          iconColor: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        };
      default:
        return {
          icon: ExclamationTriangleIcon,
          defaultTitle: 'Something went wrong',
          defaultMessage: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
          iconColor: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  const config = getErrorConfig();
  const IconComponent = config.icon;
  const displayTitle = title || config.defaultTitle;
  const displayMessage = message || config.defaultMessage;

  return (
    <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>
      <div className={`rounded-full p-4 ${config.bgColor} ${config.borderColor} border-2 mb-6`}>
        <IconComponent className={`h-12 w-12 ${config.iconColor}`} />
      </div>
      
      <h3 className="text-xl font-semibold text-gray-900 mb-3">
        {displayTitle}
      </h3>
      
      <p className="text-gray-600 mb-6 max-w-md leading-relaxed">
        {displayMessage}
      </p>
      
      {showRetryButton && onRetry && (
        <button
          onClick={onRetry}
          className="inline-flex items-center px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      )}
    </div>
  );
};

// Specific error components for common use cases
export const NetworkError: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorStates type="network" {...props} />
);

export const ServerError: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorStates type="server" {...props} />
);

export const ProductsError: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorStates type="products" {...props} />
);

export const AuthError: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorStates type="auth" {...props} />
);

export const PaymentError: React.FC<Omit<ErrorStateProps, 'type'>> = (props) => (
  <ErrorStates type="payment" {...props} />
);

// Empty state component for when no products are found
interface EmptyStateProps {
  type: 'products' | 'search' | 'cart' | 'wishlist' | 'orders';
  title?: string;
  message?: string;
  actionText?: string;
  onAction?: () => void;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  type,
  title,
  message,
  actionText,
  onAction,
  className = ''
}) => {
  const getEmptyConfig = () => {
    switch (type) {
      case 'products':
        return {
          icon: '🛍️',
          defaultTitle: 'No products available yet',
          defaultMessage: 'We\'re working hard to stock our shelves! Please check back soon for amazing products.',
          defaultActionText: 'Browse Categories',
          bgGradient: 'from-blue-50 to-indigo-50'
        };
      case 'search':
        return {
          icon: '🔍',
          defaultTitle: 'No results found',
          defaultMessage: 'We couldn\'t find any products matching your search. Try different keywords or browse our categories.',
          defaultActionText: 'Clear Search',
          bgGradient: 'from-gray-50 to-slate-50'
        };
      case 'cart':
        return {
          icon: '🛒',
          defaultTitle: 'Your cart is empty',
          defaultMessage: 'Looks like you haven\'t added anything to your cart yet. Start shopping to fill it up!',
          defaultActionText: 'Start Shopping',
          bgGradient: 'from-green-50 to-emerald-50'
        };
      case 'wishlist':
        return {
          icon: '💝',
          defaultTitle: 'Your wishlist is empty',
          defaultMessage: 'Save items you love to your wishlist so you can easily find them later.',
          defaultActionText: 'Discover Products',
          bgGradient: 'from-pink-50 to-rose-50'
        };
      case 'orders':
        return {
          icon: '📦',
          defaultTitle: 'No orders yet',
          defaultMessage: 'You haven\'t placed any orders yet. Start shopping to see your order history here.',
          defaultActionText: 'Shop Now',
          bgGradient: 'from-purple-50 to-violet-50'
        };
      default:
        return {
          icon: '📋',
          defaultTitle: 'Nothing here yet',
          defaultMessage: 'This section is empty right now.',
          defaultActionText: 'Go Back',
          bgGradient: 'from-gray-50 to-slate-50'
        };
    }
  };

  const config = getEmptyConfig();
  const displayTitle = title || config.defaultTitle;
  const displayMessage = message || config.defaultMessage;
  const displayActionText = actionText || config.defaultActionText;

  return (
    <div className={`flex flex-col items-center justify-center p-12 text-center bg-gradient-to-br ${config.bgGradient} rounded-xl ${className}`}>
      <div className="text-6xl mb-6 animate-bounce">
        {config.icon}
      </div>
      
      <h3 className="text-2xl font-bold text-gray-900 mb-4">
        {displayTitle}
      </h3>
      
      <p className="text-gray-600 mb-8 max-w-md leading-relaxed text-lg">
        {displayMessage}
      </p>
      
      {onAction && (
        <button
          onClick={onAction}
          className="inline-flex items-center px-8 py-4 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-lg"
        >
          {displayActionText}
        </button>
      )}
    </div>
  );
};

export default ErrorStates;
