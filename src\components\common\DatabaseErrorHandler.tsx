import React from 'react';
import { ServerIcon, ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface DatabaseErrorHandlerProps {
  error?: string | null;
  onRetry?: () => void;
  showRetryButton?: boolean;
  className?: string;
}

const DatabaseErrorHandler: React.FC<DatabaseErrorHandlerProps> = ({
  error,
  onRetry,
  showRetryButton = true,
  className = ''
}) => {
  if (!error) return null;

  const isDatabaseError = (errorMessage: string) => {
    const lowerError = errorMessage.toLowerCase();
    return (
      lowerError.includes('database') ||
      lowerError.includes('connection') ||
      lowerError.includes('timeout') ||
      lowerError.includes('server') ||
      lowerError.includes('mysql') ||
      lowerError.includes('sequelize') ||
      lowerError.includes('econnrefused') ||
      lowerError.includes('etimedout')
    );
  };

  const getErrorMessage = (errorMessage: string) => {
    const lowerError = errorMessage.toLowerCase();
    
    if (lowerError.includes('econnrefused') || lowerError.includes('connection refused')) {
      return 'Unable to connect to the database. Our technical team has been notified and is working to resolve this issue.';
    }
    
    if (lowerError.includes('timeout') || lowerError.includes('etimedout')) {
      return 'The database is taking longer than expected to respond. Please try again in a moment.';
    }
    
    if (lowerError.includes('database') && lowerError.includes('not found')) {
      return 'Database configuration error. Please contact support if this issue persists.';
    }
    
    if (isDatabaseError(errorMessage)) {
      return 'We\'re experiencing database connectivity issues. Please try again in a few moments.';
    }
    
    return errorMessage;
  };

  const userFriendlyMessage = getErrorMessage(error);
  const isDbError = isDatabaseError(error);

  return (
    <div className={`bg-red-50 border-2 border-red-200 rounded-lg p-6 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {isDbError ? (
            <ServerIcon className="h-8 w-8 text-red-500" />
          ) : (
            <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
          )}
        </div>
        
        <div className="ml-4 flex-1">
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            {isDbError ? 'Database Connection Error' : 'System Error'}
          </h3>
          
          <p className="text-red-700 mb-4 leading-relaxed">
            {userFriendlyMessage}
          </p>
          
          {isDbError && (
            <div className="bg-red-100 border border-red-300 rounded-lg p-4 mb-4">
              <h4 className="text-sm font-medium text-red-800 mb-2">What you can do:</h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                  Wait a few moments and try again
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                  Check your internet connection
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                  Contact support if the problem persists
                </li>
              </ul>
            </div>
          )}
          
          <div className="flex flex-col sm:flex-row gap-3">
            {showRetryButton && onRetry && (
              <button
                onClick={onRetry}
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Try Again
              </button>
            )}
            
            <button
              onClick={() => window.location.href = '/contact'}
              className="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-800 font-medium rounded-lg hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Contact Support
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Real-time data sync error handler
interface DataSyncErrorProps {
  error?: string | null;
  onRetry?: () => void;
  context?: 'inventory' | 'orders' | 'products' | 'general';
  className?: string;
}

export const DataSyncError: React.FC<DataSyncErrorProps> = ({
  error,
  onRetry,
  context = 'general',
  className = ''
}) => {
  if (!error) return null;

  const getContextMessage = (ctx: string) => {
    switch (ctx) {
      case 'inventory':
        return 'Unable to sync inventory data. Product availability may not be current.';
      case 'orders':
        return 'Unable to sync order information. Your order status may not be up to date.';
      case 'products':
        return 'Unable to sync product data. Some product information may be outdated.';
      default:
        return 'Unable to sync data with the server. Information may not be current.';
    }
  };

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800 mb-1">
            Sync Warning
          </h3>
          <p className="text-sm text-yellow-700 mb-3">
            {getContextMessage(context)}
          </p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="text-sm text-yellow-600 hover:text-yellow-800 underline font-medium"
            >
              Retry sync
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DatabaseErrorHandler;
