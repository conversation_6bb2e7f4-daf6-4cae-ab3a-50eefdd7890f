import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchProductBySlug, clearCurrentProduct } from '../store/slices/productSlice';
import { addToCart } from '../store/slices/cartSlice';
import { addToast } from '../store/slices/uiSlice';
import { ProductVariant } from '../types';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { ProductDetailSkeleton } from '../components/common/ProductLoadingStates';
import { ProductsError } from '../components/common/ErrorStates';
import ProductCard from '../components/products/ProductCard';
import GiftOptions from '../components/gift/GiftOptions';
import ProductImageGallery from '../components/products/ProductImageGallery';
import Breadcrumb, { BreadcrumbItem } from '../components/common/Breadcrumb';
import { useRecentlyViewed } from '../hooks/useRecentlyViewed';
import {
  StarIcon,
  ShoppingCartIcon,
  HeartIcon,
  ShareIcon,
  CheckIcon,
  TruckIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  GiftIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

const ProductDetail: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { addToRecentlyViewed } = useRecentlyViewed();

  const { currentProduct: product, relatedProducts, currentProductReviews: reviews, loading, error } = useAppSelector((state) => state.products);

  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState<'description' | 'effects' | 'lab-reports' | 'reviews'>('description');
  const [showGiftOptions, setShowGiftOptions] = useState(false);
  const [giftData, setGiftData] = useState({
    isGift: false,
    recipientName: '',
    recipientEmail: '',
    giftMessage: '',
    hidePrice: false,
    giftWrap: false,
    deliveryDate: undefined as string | undefined,
    recipientAddress: undefined as any
  });

  useEffect(() => {
    if (slug) {
      dispatch(fetchProductBySlug(slug));
    }

    return () => {
      dispatch(clearCurrentProduct());
    };
  }, [slug, dispatch]);

  // Add product to recently viewed when it loads
  useEffect(() => {
    if (product && !loading) {
      addToRecentlyViewed(product);
    }
  }, [product, loading, addToRecentlyViewed]);

  useEffect(() => {
    if (product?.variants && product.variants.length > 0) {
      setSelectedVariant(product.variants[0]);
    }
  }, [product]);

  const handleAddToCart = async () => {
    if (!product) return;

    if (product.trackQuantity && product.quantity < quantity) {
      dispatch(addToast({
        type: 'error',
        title: 'Insufficient Stock',
        message: 'Not enough items in stock'
      }));
      return;
    }

    try {
      await dispatch(addToCart({
        productId: product.id.toString(),
        variant: selectedVariant || undefined,
        quantity,
        giftData: giftData.isGift ? giftData : undefined
      })).unwrap();

      dispatch(addToast({
        type: 'success',
        title: 'Added to Cart',
        message: `${product.name} has been added to your cart`
      }));
    } catch (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to add item to cart'
      }));
    }
  };

  const handleGiftDataChange = (data: any) => {
    setGiftData(data);
  };

  const getCurrentPrice = () => {
    if (selectedVariant?.price) {
      return selectedVariant.price;
    }
    return product?.price || 0;
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarSolidIcon key={i} className="h-5 w-5 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <StarIcon className="h-5 w-5 text-gray-300" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <StarSolidIcon className="h-5 w-5 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-5 w-5 text-gray-300" />
        );
      }
    }

    return stars;
  };

  if (loading) {
    return <ProductDetailSkeleton />;
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <ProductsError
          title="Product Not Found"
          message={error || 'The product you are looking for does not exist or may have been removed.'}
          onRetry={() => {
            if (slug) {
              dispatch(fetchProductBySlug(slug));
            }
          }}
          showRetryButton={!!slug}
          className="min-h-[400px] flex items-center justify-center"
        />
        <div className="text-center mt-8">
          <button
            onClick={() => navigate('/shop')}
            className="btn-primary"
          >
            Continue Shopping
          </button>
        </div>
      </div>
    );
  }

  const isOutOfStock = product.trackQuantity && product.quantity <= 0;
  const isLowStock = product.trackQuantity && product.quantity <= product.lowStockThreshold && product.quantity > 0;
  const discountPercentage = product.comparePrice && product.comparePrice > product.price
    ? Math.round(((product.comparePrice - product.price) / product.comparePrice) * 100)
    : 0;

  // Generate breadcrumbs for product page
  const generateProductBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Shop', href: '/shop' }
    ];

    // Add category if available
    if (product.category) {
      breadcrumbs.push({
        label: product.category.name,
        href: `/shop?category=${product.category.slug}`
      });
    }

    // Add product name as current page
    breadcrumbs.push({
      label: product.name,
      current: true
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateProductBreadcrumbs();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbs} className="mb-8" />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* Product Images */}
          <div>
            <ProductImageGallery
              images={product.images.map(img => img.url)}
              productName={product.name}
            />

          </div>

          {/* Product Info */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-primary-600 uppercase tracking-wide">
                  {product.cannabinoid}
                </span>
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
                    <HeartIcon className="h-5 w-5" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <ShareIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>

              {product.strain && (
                <p className="text-gray-600">
                  <span className="font-medium">Strain:</span> {product.strain}
                </p>
              )}
            </div>

            {/* Rating */}
            {product.averageRating > 0 && (
              <div className="flex items-center space-x-2">
                <div className="flex items-center">
                  {renderStars(product.averageRating)}
                </div>
                <span className="text-sm text-gray-600">
                  {product.averageRating.toFixed(1)} ({product.reviewCount} reviews)
                </span>
              </div>
            )}

            {/* Price */}
            <div className="flex items-center space-x-3">
              <span className="text-3xl font-bold text-gray-900">
                ${getCurrentPrice().toFixed(2)}
              </span>
              {product.comparePrice && product.comparePrice > getCurrentPrice() && (
                <span className="text-xl text-gray-500 line-through">
                  ${product.comparePrice.toFixed(2)}
                </span>
              )}
            </div>

            {/* Short Description */}
            {product.shortDescription && (
              <p className="text-gray-700 text-lg leading-relaxed">
                {product.shortDescription}
              </p>
            )}

            {/* Variants */}
            {product.variants && product.variants.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Options</h3>
                <div className="flex flex-wrap gap-2">
                  {product.variants.map((variant, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedVariant(variant)}
                      className={`px-4 py-2 border rounded-lg text-sm font-medium transition-all ${
                        selectedVariant === variant
                          ? 'border-primary-600 bg-primary-50 text-primary-700'
                          : 'border-gray-300 text-gray-700 hover:border-gray-400'
                      }`}
                    >
                      {variant.name}: {variant.value}
                      {variant.price && variant.price !== product.price && (
                        <span className="ml-1 text-xs">
                          (+${(variant.price - product.price).toFixed(2)})
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity and Add to Cart */}
            <div className="space-y-4">
              {/* Stock Status */}
              {isOutOfStock ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 font-medium">Out of Stock</p>
                  <p className="text-red-600 text-sm">This product is currently unavailable.</p>
                </div>
              ) : (
                <>
                  {isLowStock && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                      <p className="text-orange-800 text-sm font-medium">
                        Only {product.quantity} left in stock!
                      </p>
                    </div>
                  )}

                  <div className="flex items-center space-x-4">
                    {/* Quantity Selector */}
                    <div className="flex items-center">
                      <label htmlFor="quantity" className="text-sm font-medium text-gray-700 mr-3">
                        Quantity:
                      </label>
                      <div className="flex items-center border border-gray-300 rounded-lg">
                        <button
                          onClick={() => setQuantity(Math.max(1, quantity - 1))}
                          className="px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                        >
                          -
                        </button>
                        <input
                          type="number"
                          id="quantity"
                          min="1"
                          max={product.trackQuantity ? product.quantity : 999}
                          value={quantity}
                          onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                          className="w-16 px-2 py-2 text-center border-0 focus:outline-none"
                        />
                        <button
                          onClick={() => setQuantity(Math.min(
                            product.trackQuantity ? product.quantity : 999,
                            quantity + 1
                          ))}
                          className="px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                        >
                          +
                        </button>
                      </div>
                    </div>

                    {/* Add to Cart Button */}
                    <button
                      onClick={handleAddToCart}
                      className="flex-1 bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center space-x-2"
                    >
                      <ShoppingCartIcon className="h-5 w-5" />
                      <span>Add to Cart</span>
                    </button>

                    {/* Gift Options Button */}
                    <button
                      onClick={() => setShowGiftOptions(!showGiftOptions)}
                      className="px-4 py-3 border border-primary-600 text-primary-600 rounded-lg font-medium hover:bg-primary-50 transition-colors flex items-center justify-center space-x-2"
                    >
                      <GiftIcon className="h-5 w-5" />
                      <span>Send as Gift</span>
                    </button>
                  </div>
                </>
              )}
            </div>

            {/* Gift Options */}
            {showGiftOptions && (
              <div className="mt-6">
                <GiftOptions
                  giftData={giftData}
                  onGiftDataChange={handleGiftDataChange}
                  showAddressForm={false}
                />
              </div>
            )}

            {/* Features */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-6 border-t border-gray-200">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <TruckIcon className="h-5 w-5" />
                <span>Free shipping over $100</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <ShieldCheckIcon className="h-5 w-5" />
                <span>Lab tested for purity</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <CheckIcon className="h-5 w-5" />
                <span>30-day return policy</span>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-12">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'description', label: 'Description' },
                { id: 'effects', label: 'Effects' },
                { id: 'lab-reports', label: 'Lab Reports' },
                { id: 'reviews', label: 'Reviews' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary-600 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                  {tab.id === 'reviews' && product.reviewCount > 0 && (
                    <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                      {product.reviewCount}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: product.description }} />

                {product.attributes && product.attributes.length > 0 && (
                  <div className="mt-8">
                    <h3 className="text-lg font-semibold mb-4">Product Specifications</h3>
                    <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {product.attributes.map((attr, index) => (
                        <div key={index}>
                          <dt className="font-medium text-gray-900">{attr.name}</dt>
                          <dd className="text-gray-600">{attr.value}</dd>
                        </div>
                      ))}
                    </dl>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'effects' && (
              <div>
                {product.effects && product.effects.length > 0 ? (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Expected Effects</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.effects.map((effect, index) => (
                        <span
                          key={index}
                          className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium"
                        >
                          {effect}
                        </span>
                      ))}
                    </div>
                    {product.potency && (
                      <div className="mt-6">
                        <h4 className="font-medium text-gray-900 mb-2">Potency</h4>
                        <p className="text-gray-600">{product.potency}</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-600">No effects information available for this product.</p>
                )}
              </div>
            )}

            {activeTab === 'lab-reports' && (
              <div>
                {product.labReports && product.labReports.length > 0 ? (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Lab Test Results</h3>
                    <div className="space-y-4">
                      {product.labReports.map((report, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-gray-900">{report.name}</h4>
                              <p className="text-sm text-gray-600">
                                Test Date: {new Date(report.date).toLocaleDateString()}
                              </p>
                            </div>
                            <a
                              href={report.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-medium"
                            >
                              <DocumentTextIcon className="h-5 w-5" />
                              <span>View Report</span>
                            </a>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-600">No lab reports available for this product.</p>
                )}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                {/* Reviews Summary */}
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <div className="flex items-center">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <svg
                            key={star}
                            className={`h-5 w-5 ${
                              star <= Math.floor(product.averageRating)
                                ? 'text-yellow-400'
                                : 'text-gray-300'
                            }`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="ml-2 text-lg font-semibold text-gray-900">
                        {product.averageRating.toFixed(1)}
                      </span>
                    </div>
                    <span className="text-gray-600">
                      ({product.reviewCount} review{product.reviewCount !== 1 ? 's' : ''})
                    </span>
                  </div>

                  <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    Write a Review
                  </button>
                </div>

                {/* Reviews List */}
                {reviews && reviews.length > 0 ? (
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {review.user?.firstName?.[0]}{review.user?.lastName?.[0]}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">
                                {review.user?.firstName} {review.user?.lastName}
                              </p>
                              <div className="flex items-center space-x-2">
                                <div className="flex items-center">
                                  {[1, 2, 3, 4, 5].map((star) => (
                                    <svg
                                      key={star}
                                      className={`h-4 w-4 ${
                                        star <= review.rating
                                          ? 'text-yellow-400'
                                          : 'text-gray-300'
                                      }`}
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                  ))}
                                </div>
                                <span className="text-sm text-gray-600">
                                  {new Date(review.createdAt).toLocaleDateString()}
                                </span>
                                {review.verified && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Verified Purchase
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {review.title && (
                          <h4 className="font-semibold text-gray-900 mb-2">{review.title}</h4>
                        )}

                        <p className="text-gray-700 mb-4">{review.comment}</p>

                        {review.images && review.images.length > 0 && (
                          <div className="flex space-x-2 mb-4">
                            {review.images.map((image, index) => (
                              <img
                                key={index}
                                src={image.url}
                                alt={image.alt}
                                className="w-20 h-20 object-cover rounded-lg"
                              />
                            ))}
                          </div>
                        )}

                        {review.adminResponse && (
                          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                            <p className="text-sm font-medium text-gray-900 mb-2">Response from Nirvana Organics:</p>
                            <p className="text-sm text-gray-700">{review.adminResponse}</p>
                            {review.adminResponseDate && (
                              <p className="text-xs text-gray-500 mt-2">
                                {new Date(review.adminResponseDate).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                        )}

                        <div className="flex items-center justify-between mt-4">
                          <button className="text-sm text-gray-600 hover:text-gray-900">
                            Helpful ({review.helpful})
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
                      <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No reviews yet</h3>
                    <p className="text-gray-600 mb-6">Be the first to review this product!</p>
                    <button className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                      Write the First Review
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts && relatedProducts.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">You Might Also Like</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.slice(0, 4).map((relatedProduct) => (
                <ProductCard key={relatedProduct.id} product={relatedProduct} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetail;
