import React, { useState } from 'react';
import { BestSellersSkeleton, ProductGridSkeleton, ProductDetailSkeleton, LoadingState } from './ProductLoadingStates';
import { 
  ErrorStates, 
  NetworkError, 
  ServerError, 
  ProductsError, 
  AuthError, 
  PaymentError, 
  EmptyState 
} from './ErrorStates';
import DatabaseErrorHandler, { DataSyncError } from './DatabaseErrorHandler';
import CheckoutErrorHandler, { FormErrors } from '../checkout/CheckoutErrorHandler';
import SocialMediaIcons from './SocialMediaIcons';

const ErrorHandlingDemo: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string>('loading');

  const demoSections = [
    { id: 'loading', title: 'Loading States', description: 'Various loading animations and skeleton screens' },
    { id: 'errors', title: 'Error States', description: 'Different types of error messages and handling' },
    { id: 'empty', title: 'Empty States', description: 'When no data is available' },
    { id: 'database', title: 'Database Errors', description: 'Database connection and sync issues' },
    { id: 'checkout', title: 'Checkout Errors', description: 'Payment and form validation errors' },
    { id: 'social', title: 'Social Media', description: 'Social media icons with fallbacks' }
  ];

  const renderLoadingStates = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Loading Spinner</h3>
        <LoadingState message="Loading amazing products..." size="medium" />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Product Grid Skeleton</h3>
        <ProductGridSkeleton count={4} columns={4} />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Best Sellers Skeleton</h3>
        <BestSellersSkeleton />
      </div>
    </div>
  );

  const renderErrorStates = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Network Error</h3>
        <NetworkError 
          title="Connection Problem"
          message="Unable to connect to our servers. Please check your internet connection."
          onRetry={() => alert('Retry clicked!')}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Server Error</h3>
        <ServerError 
          title="Server Error"
          message="Our servers are experiencing issues. Please try again in a few moments."
          onRetry={() => alert('Retry clicked!')}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Products Error</h3>
        <ProductsError 
          title="Products Unavailable"
          message="We're having trouble loading products right now."
          onRetry={() => alert('Retry clicked!')}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Authentication Error</h3>
        <AuthError 
          title="Authentication Required"
          message="Please log in to continue."
          onRetry={() => alert('Retry clicked!')}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Payment Error</h3>
        <PaymentError 
          title="Payment Failed"
          message="Your payment was declined. Please try a different payment method."
          onRetry={() => alert('Retry clicked!')}
        />
      </div>
    </div>
  );

  const renderEmptyStates = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">No Products</h3>
        <EmptyState
          type="products"
          title="No products available yet"
          message="We're working hard to stock our shelves! Please check back soon."
          actionText="Browse Categories"
          onAction={() => alert('Action clicked!')}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Empty Cart</h3>
        <EmptyState
          type="cart"
          title="Your cart is empty"
          message="Start shopping to fill up your cart with amazing products!"
          actionText="Start Shopping"
          onAction={() => alert('Action clicked!')}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">No Search Results</h3>
        <EmptyState
          type="search"
          title="No results found"
          message="Try different keywords or browse our categories."
          actionText="Clear Search"
          onAction={() => alert('Action clicked!')}
        />
      </div>
    </div>
  );

  const renderDatabaseErrors = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Database Connection Error</h3>
        <DatabaseErrorHandler 
          error="Database connection refused - ECONNREFUSED"
          onRetry={() => alert('Retry clicked!')}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Data Sync Warning</h3>
        <DataSyncError 
          error="Unable to sync inventory data"
          context="inventory"
          onRetry={() => alert('Retry sync clicked!')}
        />
      </div>
    </div>
  );

  const renderCheckoutErrors = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Payment Error</h3>
        <CheckoutErrorHandler 
          error="Your payment was declined by your bank"
          step="payment"
          onRetry={() => alert('Retry payment clicked!')}
          onGoBack={() => alert('Go back clicked!')}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Form Validation Errors</h3>
        <FormErrors 
          errors={{
            email: 'Please enter a valid email address',
            password: 'Password must be at least 8 characters',
            confirmPassword: 'Passwords do not match'
          }}
        />
      </div>
    </div>
  );

  const renderSocialMedia = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Social Media Icons - Default</h3>
        <SocialMediaIcons size="md" variant="default" />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Social Media Icons - Colored</h3>
        <SocialMediaIcons size="lg" variant="colored" />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Social Media Icons - With Labels</h3>
        <SocialMediaIcons size="md" variant="outline" showLabels={true} />
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeDemo) {
      case 'loading':
        return renderLoadingStates();
      case 'errors':
        return renderErrorStates();
      case 'empty':
        return renderEmptyStates();
      case 'database':
        return renderDatabaseErrors();
      case 'checkout':
        return renderCheckoutErrors();
      case 'social':
        return renderSocialMedia();
      default:
        return renderLoadingStates();
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Error Handling & Loading States Demo</h1>
        <p className="text-gray-600">
          This demo showcases all the enhanced error handling and loading states implemented in the Nirvana Organics platform.
        </p>
      </div>

      {/* Navigation */}
      <div className="flex flex-wrap gap-2 mb-8">
        {demoSections.map((section) => (
          <button
            key={section.id}
            onClick={() => setActiveDemo(section.id)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeDemo === section.id
                ? 'bg-primary-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {section.title}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            {demoSections.find(s => s.id === activeDemo)?.title}
          </h2>
          <p className="text-gray-600">
            {demoSections.find(s => s.id === activeDemo)?.description}
          </p>
        </div>
        
        {renderContent()}
      </div>
    </div>
  );
};

export default ErrorHandlingDemo;
