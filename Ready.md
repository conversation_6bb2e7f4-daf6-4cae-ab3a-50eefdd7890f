# Nirvana Organics E-commerce Platform - Deployment Guide

## 🚀 Complete Manual Deployment Instructions

This guide provides step-by-step instructions for deploying the Nirvana Organics e-commerce platform on a fresh Hostinger VPS. The deployment follows a **test-first strategy** where you deploy to the test environment first, validate everything works, then deploy to production.

## 📋 Prerequisites

### System Requirements
- Fresh Ubuntu 20.04+ VPS
- Minimum 2GB RAM, 2 CPU cores
- 50GB+ storage space
- Root or sudo access
- Domain names configured:
  - `test.shopnirvanaorganics.com` (for testing)
  - `shopnirvanaorganics.com` (for production)
  - `admin.shopnirvanaorganics.com` (for production admin)

### Required Accounts & Services
- Domain registrar access for DNS configuration
- SSL certificate provider (Let's Encrypt recommended)
- Email service (Gmail/SMTP for notifications)
- Payment gateway accounts (Square, Stripe, PayPal)
- Google OAuth credentials
- Social media API keys (optional)

## 🎯 Deployment Strategy

### Phase 1: Test Environment Deployment
1. Deploy to `/var/www/nirvana-test`
2. Configure `test.shopnirvanaorganics.com`
3. Validate all functionality
4. Test payment processing (sandbox mode)
5. Verify admin panel functionality

### Phase 2: Production Environment Deployment
1. Deploy to `/var/www/nirvana-production`
2. Configure `shopnirvanaorganics.com`
3. Set up production payment gateways
4. Configure monitoring and backups
5. Go live with real transactions

---

## 🧪 PHASE 1: TEST ENVIRONMENT DEPLOYMENT

### Step 1: Initial Server Setup

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common

# Create deployment user (optional but recommended)
sudo adduser deploy
sudo usermod -aG sudo deploy
su - deploy
```

### Step 2: Upload Test Deployment Package

```bash
# Create deployment directory
mkdir -p ~/deployments
cd ~/deployments

# Upload the Deploy/Test directory contents to this location
# You can use SCP, SFTP, or any file transfer method
# Example using SCP from your local machine:
# scp -r Deploy/Test/* user@your-server-ip:~/deployments/

# Make deployment script executable
chmod +x scripts/deploy-test.sh
```

### Step 3: Configure Environment Variables

```bash
# Edit the test environment file
nano .env.test

# Update these critical values:
# - DB_PASSWORD: Set a secure database password
# - JWT_SECRET: Generate a secure JWT secret
# - SESSION_SECRET: Generate a secure session secret
# - EMAIL_USER: Your SMTP email address
# - EMAIL_PASS: Your SMTP password/app password
# - SQUARE_APPLICATION_ID: Your Square sandbox application ID
# - SQUARE_ACCESS_TOKEN: Your Square sandbox access token
# - GOOGLE_CLIENT_ID: Your Google OAuth client ID
```

### Step 4: Run Test Deployment

```bash
# Execute the test deployment script
./scripts/deploy-test.sh

# The script will:
# - Install Node.js, MySQL, Nginx, PM2
# - Create databases and users
# - Build and deploy frontend applications
# - Configure Nginx with SSL
# - Start all services with PM2
```

### Step 5: Configure DNS for Test Domain

```bash
# Point test.shopnirvanaorganics.com to your server IP
# Add an A record in your DNS provider:
# Type: A
# Name: test
# Value: YOUR_SERVER_IP
# TTL: 300 (5 minutes)
```

### Step 6: Verify Test Deployment

```bash
# Check service status
pm2 status
sudo systemctl status nginx mysql

# Test endpoints
curl -I https://test.shopnirvanaorganics.com
curl -I https://test.shopnirvanaorganics.com/api/health
curl -I https://test.shopnirvanaorganics.com/admin

# View logs if needed
pm2 logs
sudo tail -f /var/log/nginx/nirvana-test-error.log
```

### Step 7: Test Environment Validation

1. **Frontend Testing:**
   - Visit `https://test.shopnirvanaorganics.com`
   - Test user registration and login
   - Browse products and categories
   - Test shopping cart functionality
   - Verify responsive design on mobile

2. **Admin Panel Testing:**
   - Visit `https://test.shopnirvanaorganics.com/admin`
   - Login with default credentials:
     - Email: `<EMAIL>`
     - Password: `Admin123!`
   - Test product management
   - Test order management
   - Test user management

3. **Payment Testing:**
   - Test Square payment integration (sandbox)
   - Verify payment flow completion
   - Check order creation and status

4. **Email Testing:**
   - Test user registration emails
   - Test order confirmation emails
   - Test password reset functionality

### Step 8: Test Environment Troubleshooting

```bash
# Common issues and solutions:

# If services won't start:
sudo systemctl restart nginx
pm2 restart all

# If database connection fails:
sudo systemctl restart mysql
mysql -u root -p -e "SHOW DATABASES;"

# If SSL certificate fails:
sudo certbot --nginx -d test.shopnirvanaorganics.com --force-renewal

# If frontend doesn't load:
cd /var/www/nirvana-test
npm run build:test
sudo cp -r dist/* /var/www/nirvana-frontend/test/

# Check disk space:
df -h

# Check memory usage:
free -h

# Check process status:
ps aux | grep node
```

---

## 🌟 PHASE 2: PRODUCTION ENVIRONMENT DEPLOYMENT

⚠️ **CRITICAL**: Only proceed to production deployment after the test environment is fully validated and working correctly.

### Step 1: Prepare Production Environment

```bash
# Navigate to home directory
cd ~

# Create production deployment directory
mkdir -p ~/production-deployment
cd ~/production-deployment

# Upload the Deploy/Production directory contents
# Make deployment script executable
chmod +x scripts/deploy-production.sh
```

### Step 2: Configure Production Environment Variables

```bash
# Edit production environment files
nano .env.production
nano .env.admin.production
nano .env.frontend.production
nano .env.admin.frontend.production

# CRITICAL: Update ALL placeholder values with production credentials:
# - Database passwords (use strong, unique passwords)
# - JWT secrets (generate new, secure secrets)
# - Payment gateway credentials (LIVE/PRODUCTION keys)
# - Email service credentials
# - Google OAuth credentials
# - Social media API keys
# - All "your-" placeholder values
```

### Step 3: Configure DNS for Production Domains

```bash
# Configure main domain
# Add A record for shopnirvanaorganics.com -> YOUR_SERVER_IP
# Add A record for www.shopnirvanaorganics.com -> YOUR_SERVER_IP

# Configure admin subdomain
# Add A record for admin.shopnirvanaorganics.com -> YOUR_SERVER_IP

# Wait for DNS propagation (can take up to 24 hours)
# Verify DNS resolution:
nslookup shopnirvanaorganics.com
nslookup admin.shopnirvanaorganics.com
```

### Step 4: Run Production Deployment

```bash
# Execute production deployment (requires confirmation)
./scripts/deploy-production.sh

# The script will:
# - Prompt for confirmation (type 'YES' to proceed)
# - Create comprehensive backups
# - Install and configure all services
# - Set up production-grade security
# - Configure SSL certificates for both domains
# - Deploy and start all applications
```

### Step 5: Post-Deployment Security Configuration

```bash
# Change default admin password immediately
# Visit https://admin.shopnirvanaorganics.com
# <NAME_EMAIL> / Admin123!
# Go to Profile -> Change Password

# Update firewall rules if needed
sudo ufw status
sudo ufw allow from TRUSTED_IP_ADDRESS to any port 22

# Set up automated backups
sudo crontab -e
# Add: 0 2 * * * /var/www/nirvana-production/scripts/backup-database.sh

# Configure log rotation
sudo nano /etc/logrotate.d/nirvana-organics
```

### Step 6: Configure Payment Gateways

1. **Square Production Setup:**
   - Update `.env.production` with live Square credentials
   - Set `SQUARE_ENVIRONMENT=production`
   - Test with small transaction

2. **Stripe Setup (if enabled):**
   - Update with live Stripe keys
   - Configure webhook endpoints
   - Test payment processing

3. **PayPal Setup (if enabled):**
   - Update with live PayPal credentials
   - Set `PAYPAL_MODE=live`
   - Configure IPN settings

### Step 7: Production Verification

```bash
# Check all services
pm2 status
sudo systemctl status nginx mysql redis-server

# Test main website
curl -I https://shopnirvanaorganics.com
curl -I https://shopnirvanaorganics.com/api/health

# Test admin panel
curl -I https://admin.shopnirvanaorganics.com

# Monitor logs
pm2 logs --lines 50
sudo tail -f /var/log/nginx/nirvana-prod-access.log
```

### Step 8: Go-Live Checklist

- [ ] All services running and healthy
- [ ] SSL certificates valid and auto-renewing
- [ ] Payment gateways configured and tested
- [ ] Email notifications working
- [ ] Admin panel accessible and functional
- [ ] Customer registration and login working
- [ ] Product catalog displaying correctly
- [ ] Shopping cart and checkout functional
- [ ] Order processing working end-to-end
- [ ] Mobile responsiveness verified
- [ ] Performance testing completed
- [ ] Backup systems operational
- [ ] Monitoring and alerting configured

---

## 🔧 Maintenance and Monitoring

### Daily Monitoring Commands

```bash
# Check system health
pm2 status
sudo systemctl status nginx mysql redis-server

# Check disk space
df -h

# Check memory usage
free -h

# View recent logs
pm2 logs --lines 20
sudo tail -20 /var/log/nginx/nirvana-prod-error.log

# Check SSL certificate expiry
sudo certbot certificates
```

### Backup and Recovery

```bash
# Manual database backup
mysqldump -u nirvana_prod_user -p nirvana_organics_production > backup_$(date +%Y%m%d).sql

# Restore from backup
mysql -u nirvana_prod_user -p nirvana_organics_production < backup_file.sql

# Application backup
sudo tar -czf app_backup_$(date +%Y%m%d).tar.gz /var/www/nirvana-production

# PM2 process management
pm2 restart all    # Restart all processes
pm2 reload all     # Zero-downtime reload
pm2 stop all       # Stop all processes
pm2 delete all     # Delete all processes
```

### Performance Optimization

```bash
# Enable Nginx caching (if needed)
sudo nano /etc/nginx/nginx.conf

# Optimize MySQL (if needed)
sudo mysql_secure_installation

# Monitor resource usage
htop
iotop
```

---

## 🆘 Troubleshooting Guide

### Common Issues and Solutions

1. **Website Not Loading:**
   ```bash
   # Check Nginx status
   sudo systemctl status nginx
   sudo nginx -t
   
   # Check DNS resolution
   nslookup shopnirvanaorganics.com
   
   # Check SSL certificate
   sudo certbot certificates
   ```

2. **Database Connection Errors:**
   ```bash
   # Check MySQL status
   sudo systemctl status mysql
   
   # Test database connection
   mysql -u nirvana_prod_user -p nirvana_organics_production
   
   # Check database logs
   sudo tail -f /var/log/mysql/error.log
   ```

3. **Payment Processing Issues:**
   - Verify payment gateway credentials in environment files
   - Check payment gateway dashboard for errors
   - Review application logs for payment-related errors
   - Test with small amounts first

4. **Email Not Sending:**
   - Verify SMTP credentials in environment files
   - Check email service provider settings
   - Test email configuration with simple test script

5. **High Memory Usage:**
   ```bash
   # Check memory usage by process
   ps aux --sort=-%mem | head
   
   # Restart PM2 processes if needed
   pm2 restart all
   
   # Check for memory leaks
   pm2 monit
   ```

### Emergency Procedures

1. **Site Down - Quick Recovery:**
   ```bash
   # Restart all services
   sudo systemctl restart nginx mysql redis-server
   pm2 restart all
   
   # Check logs for errors
   pm2 logs --err
   sudo tail -50 /var/log/nginx/error.log
   ```

2. **Database Corruption:**
   ```bash
   # Stop application
   pm2 stop all
   
   # Restore from latest backup
   mysql -u root -p nirvana_organics_production < latest_backup.sql
   
   # Restart application
   pm2 start all
   ```

3. **SSL Certificate Expired:**
   ```bash
   # Renew certificates
   sudo certbot renew --force-renewal
   
   # Restart Nginx
   sudo systemctl restart nginx
   ```

---

## 📞 Support and Contacts

### Technical Support
- **System Administrator**: [Your contact information]
- **Developer Support**: [Your contact information]
- **Emergency Contact**: [24/7 contact information]

### Service Providers
- **Hosting**: Hostinger Support
- **Domain**: [Your domain registrar]
- **Email**: [Your email service provider]
- **Payment Processing**: Square, Stripe, PayPal support

### Important URLs
- **Production Site**: https://shopnirvanaorganics.com
- **Admin Panel**: https://admin.shopnirvanaorganics.com
- **Test Site**: https://test.shopnirvanaorganics.com
- **Server Monitoring**: [Your monitoring dashboard URL]

---

## 🎉 Deployment Complete!

Your Nirvana Organics e-commerce platform is now successfully deployed and ready for business. The platform includes:

✅ **Customer-facing website** with modern design and full e-commerce functionality  
✅ **Admin panel** for complete business management  
✅ **Payment processing** with multiple gateway support  
✅ **User authentication** with social login options  
✅ **Product management** with categories and inventory  
✅ **Order management** with tracking and notifications  
✅ **Email notifications** for all customer interactions  
✅ **Mobile-responsive design** for all devices  
✅ **SEO optimization** for search engine visibility  
✅ **Security features** including SSL, rate limiting, and data protection  
✅ **Performance optimization** with caching and compression  
✅ **Monitoring and logging** for operational visibility  

**Remember**: This is a live production system serving real customers. Always test changes in the test environment first, maintain regular backups, and monitor system health continuously.

Good luck with your e-commerce business! 🚀
