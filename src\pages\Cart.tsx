import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchCart, clearCart } from '../store/slices/cartSlice';
import { addToast } from '../store/slices/uiSlice';
import CartItem from '../components/cart/CartItem';
import CartSummary from '../components/cart/CartSummary';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { LoadingState } from '../components/common/ProductLoadingStates';
import { EmptyState, NetworkError } from '../components/common/ErrorStates';
import SEOHead from '../components/seo/SEOHead';
import { ShoppingBagIcon, TrashIcon } from '@heroicons/react/24/outline';

const Cart: React.FC = () => {
  const dispatch = useAppDispatch();
  const { cart, loading, error } = useAppSelector((state) => state.cart);
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchCart());
    }
  }, [isAuthenticated, dispatch]);

  const handleClearCart = async () => {
    if (window.confirm('Are you sure you want to clear your entire cart? This action cannot be undone.')) {
      try {
        await dispatch(clearCart()).unwrap();
        dispatch(addToast({
          type: 'success',
          title: 'Cart Cleared',
          message: 'Your cart has been cleared successfully'
        }));
      } catch (error) {
        dispatch(addToast({
          type: 'error',
          title: 'Clear Failed',
          message: error as string || 'Failed to clear cart'
        }));
      }
    }
  };

  // Show loading state
  if (loading && !cart) {
    return (
      <div className="container mx-auto px-4 py-8">
        <LoadingState
          message="Loading your cart..."
          size="large"
          className="min-h-[400px]"
        />
      </div>
    );
  }

  // Show error state
  if (error && !cart) {
    const isNetworkError = error.toLowerCase().includes('network') || error.toLowerCase().includes('connection');

    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">Shopping Cart</h1>
        {isNetworkError ? (
          <NetworkError
            title="Unable to Load Cart"
            message="We're having trouble loading your cart. Please check your connection and try again."
            onRetry={() => dispatch(fetchCart())}
            className="max-w-md mx-auto"
          />
        ) : (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={() => dispatch(fetchCart())}
              className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    );
  }

  // Show empty cart state
  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">Shopping Cart</h1>
        <EmptyState
          type="cart"
          title="Your cart is empty"
          message="Looks like you haven't added anything to your cart yet. Start shopping to fill it up!"
          actionText="Start Shopping"
          onAction={() => window.location.href = '/shop'}
          className="max-w-lg mx-auto"
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <SEOHead
        title="Shopping Cart - Review Your Cannabis Products"
        description="Review your selected cannabis products before checkout. Secure shopping cart with premium hemp-derived products. Modify quantities or proceed to secure checkout."
        keywords={['cannabis cart', 'hemp shopping cart', 'cannabis checkout', 'review order', 'cannabis products']}
        canonicalUrl="/cart"
        noIndex={true}
      />
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Shopping Cart ({cart.items.length} {cart.items.length === 1 ? 'item' : 'items'})
        </h1>

        {cart.items.length > 0 && (
          <button
            onClick={handleClearCart}
            className="flex items-center text-red-600 hover:text-red-700 transition-colors"
            disabled={loading}
          >
            <TrashIcon className="h-5 w-5 mr-2" />
            Clear Cart
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {cart.items.map((item, index) => (
              <CartItem
                key={`${item.productId}-${JSON.stringify(item.variant)}`}
                item={item}
                itemIndex={index}
                loading={loading}
              />
            ))}
          </div>
        </div>

        {/* Cart Summary */}
        <div className="lg:col-span-1">
          <div className="sticky top-4">
            <CartSummary cart={cart} loading={loading} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
