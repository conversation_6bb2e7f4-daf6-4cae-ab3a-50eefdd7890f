# Nirvana Organics - Test Environment Configuration
# PHASE 1: Test-First Deployment Strategy
# Main Server Configuration for TEST ENVIRONMENT ONLY
# Deploy to test.shopnirvanaorganics.com FIRST for validation
# DO NOT use production values here

# Environment
NODE_ENV=testing

# Server Configuration
PORT=5000
FRONTEND_URL=https://test.shopnirvanaorganics.com
BACKEND_URL=https://test.shopnirvanaorganics.com

# Database Configuration (Test)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_test
DB_USER=root
DB_PASSWORD=NirvanaTest123!
DB_SSL=false
DB_POOL_MAX=8
DB_POOL_MIN=2

# CORS Configuration (Path-based admin panel)
CORS_ORIGIN=https://test.shopnirvanaorganics.com

# Security Configuration
JWT_SECRET=test_jwt_secret_change_in_production_4d18f78eb794845484ea3fa05759c0e4412b0987c054ce0e2009050f3ef4fefc
JWT_REFRESH_SECRET=test_refresh_secret_change_in_production_zvBFWhW0eP3uNAp0t0KLIRq0owdq1QX82OmLLXyMpSY=
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=10

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
MAIN_RATE_LIMIT_MAX_REQUESTS=1000

# Email Configuration (Test)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-test-email-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Nirvana Organics Test

# Email Addresses (Test Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_SUPPORT=<EMAIL>
EMAIL_CUSTOMER_SERVICE=<EMAIL>

# Square Payment Configuration (Test/Sandbox)
SQUARE_APPLICATION_ID=your-square-sandbox-application-id
SQUARE_ACCESS_TOKEN=your-square-sandbox-access-token
SQUARE_WEBHOOK_SIGNATURE_KEY=your-square-sandbox-webhook-signature
SQUARE_ENVIRONMENT=sandbox
SQUARE_LOCATION_ID=your-square-sandbox-location-id

# Google OAuth Configuration (Test)
GOOGLE_CLIENT_ID=your-test-google-client-id
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
GOOGLE_REDIRECT_URI=https://test.shopnirvanaorganics.com/auth/google/callback

# Push Notifications (Test)
VAPID_PUBLIC_KEY=your-test-vapid-public-key
VAPID_PRIVATE_KEY=your-test-vapid-private-key
VAPID_SUBJECT=mailto:<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp
ALLOWED_DOCUMENT_TYPES=pdf,doc,docx

# Stripe Configuration (Test)
STRIPE_SECRET_KEY=sk_test_your_stripe_test_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_test_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_test_webhook_secret

# PayPal Configuration (Test)
PAYPAL_CLIENT_ID=your-paypal-sandbox-client-id
PAYPAL_CLIENT_SECRET=your-paypal-sandbox-client-secret
PAYPAL_MODE=sandbox

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE=logs/test.log
ERROR_LOG_FILE=logs/test-error.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=1800

# Analytics Configuration
GOOGLE_ANALYTICS_ID=your-test-google-analytics-id

# Social Media Configuration (Test)
FACEBOOK_APP_ID=your-test-facebook-app-id
FACEBOOK_APP_SECRET=your-test-facebook-app-secret
INSTAGRAM_ACCESS_TOKEN=your-test-instagram-access-token
TWITTER_API_KEY=your-test-twitter-api-key
TWITTER_API_SECRET=your-test-twitter-api-secret

# Shipping Configuration
USPS_USER_ID=your-test-usps-user-id
USPS_API_URL=https://secure.shippingapis.com/ShippingAPI.dll

# Security Headers
MAIN_SECURITY_MODE=true
ENABLE_HTTPS_REDIRECT=true

# Session Configuration
SESSION_SECRET=test_session_secret_change_in_production
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict

# API Keys
OPENAI_API_KEY=your-test-openai-api-key

# WhatsApp Configuration (Test)
WHATSAPP_PHONE_NUMBER_ID=your-test-whatsapp-phone-number-id
WHATSAPP_ACCESS_TOKEN=your-test-whatsapp-access-token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-test-whatsapp-webhook-verify-token

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1

# Feature Flags
ENABLE_SOCIAL_LOGIN=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true
ENABLE_CHAT_SUPPORT=false
ENABLE_REAL_TIME_UPDATES=true

# Development/Debug Settings
DEBUG_MODE=true
ENABLE_CONSOLE_LOGS=true
ENABLE_REQUEST_LOGGING=true
ENABLE_SQL_LOGGING=false

# API Timeouts
API_TIMEOUT=30000
DATABASE_TIMEOUT=10000
EMAIL_TIMEOUT=15000

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# GDPR Compliance
ENABLE_COOKIE_CONSENT=true
ENABLE_DATA_EXPORT=true
ENABLE_DATA_DELETION=true

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=We are currently performing scheduled maintenance. Please check back soon.

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
