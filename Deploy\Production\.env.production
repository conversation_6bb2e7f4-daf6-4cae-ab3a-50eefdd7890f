# Nirvana Organics - Production Environment Configuration
# PRODUCTION ENVIRONMENT - LIVE SYSTEM
# Main Server Configuration for PRODUCTION ENVIRONMENT
# Deploy to shopnirvanaorganics.com ONLY after successful test deployment

# Environment
NODE_ENV=production

# Server Configuration
PORT=5000
FRONTEND_URL=https://shopnirvanaorganics.com
BACKEND_URL=https://shopnirvanaorganics.com

# Database Configuration (Production)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_production
DB_USER=nirvana_prod_user
DB_PASSWORD=CHANGE_THIS_SECURE_PRODUCTION_PASSWORD
DB_SSL=true
DB_POOL_MAX=20
DB_POOL_MIN=5

# CORS Configuration
CORS_ORIGIN=https://shopnirvanaorganics.com

# Security Configuration (Production Grade)
JWT_SECRET=CHANGE_THIS_PRODUCTION_JWT_SECRET_MINIMUM_32_CHARACTERS_LONG
JWT_REFRESH_SECRET=CHANGE_THIS_PRODUCTION_REFRESH_SECRET_MINIMUM_32_CHARACTERS_LONG
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Rate Limiting (Production)
RATE_LIMIT_WINDOW_MS=900000
MAIN_RATE_LIMIT_MAX_REQUESTS=2000

# Email Configuration (Production)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-production-email-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Nirvana Organics

# Email Addresses (Production)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_SUPPORT=<EMAIL>
EMAIL_CUSTOMER_SERVICE=<EMAIL>

# Square Payment Configuration (Production)
SQUARE_APPLICATION_ID=your-production-square-application-id
SQUARE_ACCESS_TOKEN=your-production-square-access-token
SQUARE_WEBHOOK_SIGNATURE_KEY=your-production-square-webhook-signature
SQUARE_ENVIRONMENT=production
SQUARE_LOCATION_ID=your-production-square-location-id

# Google OAuth Configuration (Production)
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
GOOGLE_REDIRECT_URI=https://shopnirvanaorganics.com/auth/google/callback

# Push Notifications (Production)
VAPID_PUBLIC_KEY=your-production-vapid-public-key
VAPID_PRIVATE_KEY=your-production-vapid-private-key
VAPID_SUBJECT=mailto:<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp
ALLOWED_DOCUMENT_TYPES=pdf,doc,docx

# Stripe Configuration (Production)
STRIPE_SECRET_KEY=sk_live_your_stripe_production_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_production_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_production_webhook_secret

# PayPal Configuration (Production)
PAYPAL_CLIENT_ID=your-paypal-production-client-id
PAYPAL_CLIENT_SECRET=your-paypal-production-client-secret
PAYPAL_MODE=live

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/production.log
ERROR_LOG_FILE=logs/production-error.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Analytics Configuration
GOOGLE_ANALYTICS_ID=your-production-google-analytics-id

# Social Media Configuration (Production)
FACEBOOK_APP_ID=your-production-facebook-app-id
FACEBOOK_APP_SECRET=your-production-facebook-app-secret
INSTAGRAM_ACCESS_TOKEN=your-production-instagram-access-token
TWITTER_API_KEY=your-production-twitter-api-key
TWITTER_API_SECRET=your-production-twitter-api-secret

# Shipping Configuration
USPS_USER_ID=your-production-usps-user-id
USPS_API_URL=https://secure.shippingapis.com/ShippingAPI.dll

# Security Headers
MAIN_SECURITY_MODE=true
ENABLE_HTTPS_REDIRECT=true

# Session Configuration
SESSION_SECRET=CHANGE_THIS_PRODUCTION_SESSION_SECRET_MINIMUM_32_CHARACTERS
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict

# API Keys
OPENAI_API_KEY=your-production-openai-api-key

# WhatsApp Configuration (Production)
WHATSAPP_PHONE_NUMBER_ID=your-production-whatsapp-phone-number-id
WHATSAPP_ACCESS_TOKEN=your-production-whatsapp-access-token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-production-whatsapp-webhook-verify-token

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=90

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1

# Feature Flags
ENABLE_SOCIAL_LOGIN=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true
ENABLE_CHAT_SUPPORT=true
ENABLE_REAL_TIME_UPDATES=true

# Development/Debug Settings (Production - Disabled)
DEBUG_MODE=false
ENABLE_CONSOLE_LOGS=false
ENABLE_REQUEST_LOGGING=false
ENABLE_SQL_LOGGING=false

# API Timeouts
API_TIMEOUT=30000
DATABASE_TIMEOUT=10000
EMAIL_TIMEOUT=15000

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# GDPR Compliance
ENABLE_COOKIE_CONSENT=true
ENABLE_DATA_EXPORT=true
ENABLE_DATA_DELETION=true

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=We are currently performing scheduled maintenance. Please check back soon.

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# SSL Configuration
SSL_CERT_PATH=/etc/letsencrypt/live/shopnirvanaorganics.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/shopnirvanaorganics.com/privkey.pem

# Monitoring and Alerting
SENTRY_DSN=your-production-sentry-dsn
ENABLE_ERROR_TRACKING=true
ENABLE_UPTIME_MONITORING=true

# CDN Configuration
CDN_URL=https://cdn.shopnirvanaorganics.com
ASSETS_URL=https://shopnirvanaorganics.com/assets

# Search Engine Optimization
ENABLE_SITEMAP_GENERATION=true
ENABLE_ROBOTS_TXT=true
ENABLE_STRUCTURED_DATA=true

# Security Enhancements
ENABLE_RATE_LIMITING=true
ENABLE_DDOS_PROTECTION=true
ENABLE_IP_BLOCKING=true
BLOCKED_IPS=

# Compliance
ENABLE_GDPR_COMPLIANCE=true
ENABLE_CCPA_COMPLIANCE=true
ENABLE_AGE_VERIFICATION=true
MINIMUM_AGE=21

# Third-party Integrations
MAILCHIMP_API_KEY=your-production-mailchimp-api-key
MAILCHIMP_LIST_ID=your-production-mailchimp-list-id

# Customer Support
ZENDESK_SUBDOMAIN=your-zendesk-subdomain
ZENDESK_API_TOKEN=your-zendesk-api-token

# Business Intelligence
ENABLE_ANALYTICS_TRACKING=true
ENABLE_CONVERSION_TRACKING=true
ENABLE_HEAT_MAPPING=false
