import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { login, clearError } from '../store/slices/authSlice';
import { addToast } from '../store/slices/uiSlice';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { AuthError } from '../components/common/ErrorStates';
import SEOHead from '../components/seo/SEOHead';
import SocialLogin from '../components/auth/SocialLogin';

const Login: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  const { loading, error, isAuthenticated, user } = useAppSelector((state) => state.auth);

  const from = location.state?.from?.pathname || '/';

  useEffect(() => {
    if (isAuthenticated && user) {
      // Determine redirect destination based on user role and from location
      let redirectTo = from;

      // If no specific 'from' location and user is admin, redirect to admin dashboard
      if (from === '/' && (user.role === 'admin' || user.role === 'super_admin')) {
        redirectTo = '/admin';
      }

      navigate(redirectTo, { replace: true });
    }
  }, [isAuthenticated, user, navigate, from]);

  useEffect(() => {
    if (error) {
      dispatch(addToast({
        type: 'error',
        title: 'Login Failed',
        message: error,
      }));
      dispatch(clearError());
    }
  }, [error, dispatch]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email || !formData.password) {
      dispatch(addToast({
        type: 'error',
        title: 'Validation Error',
        message: 'Please fill in all fields',
      }));
      return;
    }

    try {
      await dispatch(login(formData)).unwrap();
      dispatch(addToast({
        type: 'success',
        title: 'Login Successful',
        message: 'Welcome back!',
      }));
    } catch (error) {
      // Error is handled by useEffect above
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <SEOHead
        title="Sign In - Access Your Cannabis Account"
        description="Sign in to your Nirvana Organics account to access your orders, track shipments, and manage your cannabis product purchases. Secure login for premium hemp products."
        keywords={['cannabis login', 'hemp account', 'sign in', 'cannabis account', 'hemp login', 'user account']}
        canonicalUrl="/login"
        noIndex={true}
      />

      {/* Amazon-style container with shadow and border */}
      <div className="max-w-md w-full">
        {/* Logo and branding section */}
        <div className="text-center mb-8">
          <div className="mx-auto h-16 w-auto flex justify-center mb-4">
            <img
              src="/Nirvana_logo.png"
              alt="Nirvana Organics"
              className="h-16 w-auto"
              onError={(e) => {
                // Fallback to text if logo fails to load
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>
          <h1 className="text-2xl font-bold text-brand-dark-green-900 mb-2">
            Nirvana Organics
          </h1>
        </div>

        {/* Main login card - Amazon style */}
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-8">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-brand-dark-green-900 mb-2">
              Sign in
            </h2>
            <p className="text-sm text-gray-600">
              Enter your email and password to access your account
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* Email field - Amazon style */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-brand-dark-green-900 mb-2">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="w-full px-3 py-3 border border-gray-300 rounded-md text-sm placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-brand-yellow-500 focus:border-brand-yellow-500 transition-colors duration-200"
                placeholder="Enter your email address"
                value={formData.email}
                onChange={handleChange}
              />
            </div>

            {/* Password field - Amazon style */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-brand-dark-green-900 mb-2">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  className="w-full px-3 py-3 pr-10 border border-gray-300 rounded-md text-sm placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-brand-yellow-500 focus:border-brand-yellow-500 transition-colors duration-200"
                  placeholder="Enter your password"
                  value={formData.password}
                  onChange={handleChange}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  )}
                </button>
              </div>
            </div>

            {/* Remember me and forgot password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-brand-yellow-600 focus:ring-brand-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-brand-dark-green-800">
                  Keep me signed in
                </label>
              </div>

              <div className="text-sm">
                <Link
                  to="/forgot-password"
                  className="font-medium text-brand-yellow-600 hover:text-brand-yellow-700 transition-colors duration-200"
                >
                  Forgot password?
                </Link>
              </div>
            </div>

            {/* Amazon-style submit button */}
            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-brand-yellow-500 hover:bg-brand-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm"
              >
                {loading ? (
                  <LoadingSpinner size="small" color="white" />
                ) : (
                  'Sign In'
                )}
              </button>
            </div>

            {/* Divider */}
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Or continue with</span>
              </div>
            </div>

            {/* Social Login */}
            <SocialLogin
              onSuccess={() => navigate(from, { replace: true })}
              onError={(error) => {
                dispatch(addToast({
                  type: 'error',
                  title: 'Social Login Failed',
                  message: error,
                }));
              }}
            />
          </form>
        </div>

        {/* Create account section - Amazon style */}
        <div className="mt-6 bg-white rounded-lg shadow border border-gray-200 p-6 text-center">
          <p className="text-sm text-gray-600 mb-3">
            New to Nirvana Organics?
          </p>
          <Link
            to="/register"
            className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-brand-dark-green-900 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-yellow-500 transition-colors duration-200"
          >
            Create your Nirvana account
          </Link>
        </div>

        {/* Footer links */}
        <div className="mt-6 text-center">
          <div className="flex justify-center space-x-6 text-xs text-gray-500">
            <Link to="/privacy" className="hover:text-brand-yellow-600 transition-colors duration-200">
              Privacy Policy
            </Link>
            <Link to="/terms" className="hover:text-brand-yellow-600 transition-colors duration-200">
              Terms of Service
            </Link>
            <Link to="/help" className="hover:text-brand-yellow-600 transition-colors duration-200">
              Help
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
