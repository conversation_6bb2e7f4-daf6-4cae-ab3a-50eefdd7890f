# Nirvana Organics - Admin Test Environment Configuration
# Admin Server Configuration for TEST ENVIRONMENT ONLY

# Environment
NODE_ENV=testing

# Server Configuration
PORT=3001
FRONTEND_URL=https://test.shopnirvanaorganics.com
BACKEND_URL=https://test.shopnirvanaorganics.com

# Database Configuration (Test)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=nirvana_organics_admin_test
DB_USER=root
DB_PASSWORD=NirvanaTest123!
DB_SSL=false
DB_POOL_MAX=8
DB_POOL_MIN=2

# CORS Configuration
CORS_ORIGIN=https://test.shopnirvanaorganics.com

# Security Configuration
JWT_SECRET=test_admin_jwt_secret_change_in_production_4d18f78eb794845484ea3fa05759c0e4412b0987c054ce0e2009050f3ef4fefc
JWT_REFRESH_SECRET=test_admin_refresh_secret_change_in_production_zvBFWhW0eP3uNAp0t0KLIRq0owdq1QX82OmLLXyMpSY=
JWT_EXPIRES_IN=8h
JWT_REFRESH_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# Rate Limiting (Stricter for admin)
RATE_LIMIT_WINDOW_MS=900000
ADMIN_RATE_LIMIT_MAX_REQUESTS=500

# Email Configuration (Test)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-test-email-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Nirvana Organics Admin Test

# Admin Email Addresses
EMAIL_ADMIN=<EMAIL>
EMAIL_NOTIFICATIONS=<EMAIL>

# Square Payment Configuration (Test/Sandbox)
SQUARE_APPLICATION_ID=your-square-sandbox-application-id
SQUARE_ACCESS_TOKEN=your-square-sandbox-access-token
SQUARE_WEBHOOK_SIGNATURE_KEY=your-square-sandbox-webhook-signature
SQUARE_ENVIRONMENT=sandbox
SQUARE_LOCATION_ID=your-square-sandbox-location-id

# Google OAuth Configuration (Test)
GOOGLE_CLIENT_ID=your-test-google-client-id
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
GOOGLE_REDIRECT_URI=https://test.shopnirvanaorganics.com/admin/auth/google/callback

# File Upload Configuration
MAX_FILE_SIZE=52428800
UPLOAD_PATH=uploads
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp
ALLOWED_DOCUMENT_TYPES=pdf,doc,docx,xls,xlsx,csv

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE=logs/admin-test.log
ERROR_LOG_FILE=logs/admin-test-error.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Analytics Configuration
GOOGLE_ANALYTICS_ID=your-test-google-analytics-id

# Security Headers
ADMIN_SECURITY_MODE=true
ENABLE_HTTPS_REDIRECT=true

# Session Configuration (More secure for admin)
SESSION_SECRET=test_admin_session_secret_change_in_production
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict
SESSION_TIMEOUT=28800000

# Admin-specific Features
ENABLE_ADMIN_ANALYTICS=true
ENABLE_DATA_EXPORT=true
ENABLE_BULK_OPERATIONS=true
ENABLE_ADVANCED_REPORTING=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 3 * * *
BACKUP_RETENTION_DAYS=90

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=1.0

# Development/Debug Settings
DEBUG_MODE=true
ENABLE_CONSOLE_LOGS=true
ENABLE_REQUEST_LOGGING=true
ENABLE_SQL_LOGGING=true

# API Timeouts
API_TIMEOUT=60000
DATABASE_TIMEOUT=30000
EMAIL_TIMEOUT=30000

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# Admin Access Control
ADMIN_IP_WHITELIST=
ADMIN_2FA_REQUIRED=false
ADMIN_SESSION_TIMEOUT=28800000

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=Admin panel is currently under maintenance. Please check back soon.

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# Data Environment Configuration
ENABLE_DATA_ENVIRONMENT=true
DATA_ENVIRONMENT_MODE=test
